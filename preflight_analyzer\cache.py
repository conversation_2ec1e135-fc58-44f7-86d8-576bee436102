"""
Cache Interface - Drop-in replacement for DecisionHistory.

This module provides a backward-compatible interface that uses the new
multi-layer cache architecture while maintaining the same API as the
legacy DecisionHistory class.
"""

from __future__ import annotations
import logging
import time
from pathlib import Path
from typing import Any, Dict, Optional

from .cache_models import <PERSON><PERSON><PERSON><PERSON>, Content<PERSON>ey, AnalysisMode, extract_quality_from_title
from .multi_layer_cache import MultiLayerCache
from .cache_observability import <PERSON>acheMetric<PERSON>, <PERSON>acheLogger, CacheHealthMonitor
from .cache_migration import CacheMigrator

logger = logging.getLogger(__name__)


class DecisionCache:
    """
    Cache that replaces the legacy DecisionHistory class.
    
    Provides the same interface as DecisionHistory but uses the new
    multi-layer cache architecture underneath for better performance
    and GUID reconciliation capabilities.
    """
    
    def __init__(self, cache_file_path: Path, ttl_seconds: int = 12 * 3600):
        """
        Initialize cache.
        
        Args:
            cache_file_path: Path where legacy cache was stored (used for migration)
            ttl_seconds: TTL for memory cache (persistent cache has longer retention)
        """
        self.legacy_cache_path = cache_file_path
        self.cache_dir = cache_file_path.parent / "cache"
        
        # Initialize observability
        self.metrics = CacheMetrics()
        self.cache_logger = CacheLogger(self.metrics)
        self.health_monitor = CacheHealthMonitor(self.metrics)
        
        # Initialize multi-layer cache
        self.cache = MultiLayerCache(
            cache_dir=self.cache_dir,
            memory_ttl_seconds=ttl_seconds,
            persistent_max_age_days=365  # Much longer retention than legacy
        )
        
        # Migrate legacy data if it exists
        self._migrate_legacy_data()
        
        logger.info(f"Initialized decision cache at {self.cache_dir}")
    
    def get(self, key: str, analysis_mode: str = "standard") -> Optional[Dict[str, Any]]:
        """
        Get cached analysis result by key.
        
        This maintains compatibility with the legacy DecisionHistory.get() method
        but uses the new multi-layer cache with GUID reconciliation.
        
        Args:
            key: Cache key in format "Indexer:GUID"
            analysis_mode: Analysis mode ('standard', 'reliability', 'hybrid')

        Returns:
            Dictionary with 'decision', 'risk', 'missing_ratio' keys or None
        """
        start_time = time.time()
        
        try:
            # Parse legacy key format
            if ':' not in key:
                self.cache_logger.log_cache_error('legacy_api', key, "Invalid key format")
                return None
            
            indexer, guid = key.split(':', 1)
            
            # Use multi-layer cache to get analysis
            # We don't have title/size info from the legacy API, so we'll do a simple GUID lookup
            mode = AnalysisMode.from_string(analysis_mode)
            result = self.cache.get_analysis(
                guid=guid,
                title="Unknown",  # Will be ignored for GUID-only lookups
                size_bytes=0,     # Will be ignored for GUID-only lookups
                indexer=indexer,
                analysis_mode=mode
            )
            
            latency_ms = (time.time() - start_time) * 1000
            
            if result:
                # Convert to legacy format
                legacy_result = {
                    'decision': result.decision,
                    'risk': result.risk_score,
                    'missing_ratio': result.probe_missing_ratio,
                    'ts': result.timestamp
                }
                
                self.cache_logger.log_cache_hit(
                    'cache_api', guid, result.content_key.to_string(), 
                    latency_ms, indexer
                )
                
                return legacy_result
            else:
                self.cache_logger.log_cache_miss('cache_api', guid, latency_ms, indexer)
                return None
                
        except Exception as e:
            latency_ms = (time.time() - start_time) * 1000
            self.cache_logger.log_cache_error('cache_api', key, str(e), latency_ms)
            return None
    
    def put(self, key: str, decision: str, report: Dict[str, Any], analysis_mode: str = "standard") -> None:
        """
        Store analysis result in cache.
        
        This maintains compatibility with the legacy DecisionHistory.put() method
        but stores in the new multi-layer cache with content-based keys.
        
        Args:
            key: Cache key in format "Indexer:GUID"
            decision: Analysis decision ('ACCEPT', 'REJECT_INCOMPLETE', etc.)
            report: Full analysis report dictionary
        """
        start_time = time.time()
        
        try:
            # Parse legacy key format
            if ':' not in key:
                self.cache_logger.log_cache_error('cache_api', key, "Invalid key format")
                return
            
            indexer, guid = key.split(':', 1)
            
            # Extract metadata from report to create proper AnalysisResult
            title = report.get('title', 'Unknown')
            
            # Try to determine content type and create content key
            content_key = self._create_content_key_from_report(report, title)
            
            # Create AnalysisResult
            # Ensure all required fields have safe defaults, especially handle None values
            missing_ratio = report.get('probe_missing_ratio')
            safe_missing_ratio = missing_ratio if missing_ratio is not None else 0.0
            
            analysis_result = AnalysisResult(
                content_key=content_key,
                decision=decision,
                risk_score=report.get('risk_score', 0.0),
                probe_missing_ratio=safe_missing_ratio,
                file_count=report.get('file_count', 0),
                data_segments=report.get('data_segments', 0),
                estimated_parity_blocks=report.get('estimated_parity_blocks', 0),
                age_days=report.get('age_days', 0),
                poster=report.get('poster', ''),
                groups=report.get('groups', []),
                size_bytes=report.get('size', 0),
                indexer=indexer,
                title=title,
                guid=guid,
                timestamp=time.time(),
                analysis_version="enhanced",
                sample_size=report.get('sample_size'),
                probe_error_ratio=report.get('probe_error_ratio'),
                risk_components=report.get('components')
            )
            
            # Store in multi-layer cache
            success = self.cache.put_analysis(analysis_result)
            
            latency_ms = (time.time() - start_time) * 1000
            
            if success:
                self.cache_logger.log_cache_put(
                    'cache_api', guid, content_key.to_string(),
                    latency_ms, indexer
                )
            else:
                self.cache_logger.log_cache_error(
                    'cache_api', guid, "Failed to store analysis result", latency_ms
                )
                
        except Exception as e:
            latency_ms = (time.time() - start_time) * 1000
            self.cache_logger.log_cache_error('cache_api', key, str(e), latency_ms)
    
    def _create_content_key_from_report(self, report: Dict[str, Any], title: str) -> ContentKey:
        """Create content key from analysis report."""
        # Try to determine if this is a movie or TV show
        content_type = 'movie'  # Default assumption
        season = episode = year = None
        
        # Look for TV patterns in title
        import re
        tv_patterns = [
            r'[Ss](\d{1,2})[Ee](\d{1,2})',  # S01E05
            r'(\d{1,2})x(\d{1,2})',         # 1x05
        ]
        
        for pattern in tv_patterns:
            match = re.search(pattern, title)
            if match:
                content_type = 'tv'
                season = int(match.group(1))
                episode = int(match.group(2))
                break
        
        # Look for year in title (for movies)
        if content_type == 'movie':
            year_match = re.search(r'\b(19|20)(\d{2})\b', title)
            if year_match:
                year = int(year_match.group(0))
        
        # Extract quality
        quality = extract_quality_from_title(title)
        
        # Clean title
        clean_title = self._clean_title_for_content_key(title, content_type)
        
        return ContentKey(
            content_type=content_type,
            title=clean_title,
            year=year,
            season=season,
            episode=episode,
            quality=quality
        )
    
    def _clean_title_for_content_key(self, title: str, content_type: str) -> str:
        """Clean title for content key generation."""
        import re
        
        clean_title = title
        
        # Remove quality indicators
        quality_patterns = [
            r'\b\d+p\b', r'\b4k\b', r'\buhd\b', r'\bweb-?dl\b', r'\bbluray\b',
            r'\bhdtv\b', r'\bx264\b', r'\bx265\b', r'\bhevc\b'
        ]
        for pattern in quality_patterns:
            clean_title = re.sub(pattern, '', clean_title, flags=re.IGNORECASE)
        
        if content_type == 'movie':
            # Remove year for movies
            clean_title = re.sub(r'\b(19|20)\d{2}\b', '', clean_title)
        else:  # TV
            # Remove season/episode info for TV
            clean_title = re.sub(r'[Ss]\d{1,2}[Ee]\d{1,2}', '', clean_title)
            clean_title = re.sub(r'\d{1,2}x\d{1,2}', '', clean_title)
        
        # Clean up punctuation and spacing
        clean_title = re.sub(r'[._-]', ' ', clean_title)
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        return clean_title
    
    def _migrate_legacy_data(self):
        """Migrate data from legacy DecisionHistory if it exists."""
        if not self.legacy_cache_path.exists():
            return
        
        logger.info(f"Migrating legacy cache data from {self.legacy_cache_path}")
        
        migrator = CacheMigrator(self.cache)
        stats = migrator.migrate_decision_history(self.legacy_cache_path)
        
        logger.info(f"Migration completed: {stats}")
        
        # Create backup of legacy file
        backup_path = self.legacy_cache_path.with_suffix('.json.backup')
        if not backup_path.exists():
            try:
                import shutil
                shutil.copy2(self.legacy_cache_path, backup_path)
                logger.info(f"Created backup of legacy cache at {backup_path}")
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return self.cache.get_cache_stats()
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get cache health status."""
        return self.health_monitor.check_health()
    
    def get_performance_report(self) -> str:
        """Get human-readable performance report."""
        return self.health_monitor.get_performance_report()
    
    def cleanup_expired(self) -> int:
        """Clean up expired cache entries."""
        return self.cache.cleanup_expired()
    
    def close(self):
        """Close cache and cleanup resources."""
        # Log final stats
        self.cache_logger.log_cache_stats_summary()
        
        # Export metrics
        metrics_path = self.cache_dir / "metrics.json"
        self.metrics.export_metrics(metrics_path)
        
        # Close cache
        self.cache.close()
        
        logger.info("Decision cache closed")


# Legacy compatibility alias
DecisionHistory = DecisionCache
