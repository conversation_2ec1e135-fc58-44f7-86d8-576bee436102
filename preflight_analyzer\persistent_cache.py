"""
L2 Persistent Cache Layer - SQLite-based durable cache for analysis results.

This module implements the persistent storage layer of the multi-layer cache architecture.
It provides long-term storage of analysis results with content-based keys and GUID mapping.
"""

from __future__ import annotations
import json
import logging
import sqlite3
import threading
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from .cache_models import (
    AnalysisResult, CacheEntry, CacheSchema, ContentKey,
    extract_quality_from_title, parse_movie_content_key, parse_tv_content_key
)
from .ttl_coordinator import get_ttl_coordinator, TTLPolicy

logger = logging.getLogger(__name__)


class PersistentCache:
    """L2 persistent cache using SQLite for durable analysis storage."""
    
    def __init__(self, db_path: Path, max_age_days: int = 365):
        self.db_path = db_path
        self.max_age_days = max_age_days
        self._conn_lock = threading.RLock()
        self._local = threading.local()
        
        # Initialize database
        self._init_connection()
        logger.info(f"Initialized persistent cache at {db_path}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection."""
        if not hasattr(self._local, 'conn') or self._local.conn is None:
            self._local.conn = sqlite3.connect(
                str(self.db_path), 
                check_same_thread=False,
                timeout=30.0
            )
            self._local.conn.execute("PRAGMA foreign_keys = ON")
            self._local.conn.execute("PRAGMA journal_mode = WAL")
            self._local.conn.row_factory = sqlite3.Row
        
        return self._local.conn
    
    def _init_connection(self):
        """Initialize the database schema."""
        with self._conn_lock:
            conn = CacheSchema.initialize_database(self.db_path)
            conn.close()
    
    def get_by_content_key(self, content_key: ContentKey) -> Optional[AnalysisResult]:
        """Retrieve analysis result by content key."""
        conn = self._get_connection()
        
        try:
            cursor = conn.execute(
                """
                SELECT * FROM analysis_cache 
                WHERE content_key_hash = ?
                """,
                (content_key.to_hash(),)
            )
            
            row = cursor.fetchone()
            if not row:
                return None
            
            # Update access tracking
            self._update_access_stats(content_key.to_hash())
            
            # Reconstruct AnalysisResult
            return self._row_to_analysis_result(row)
            
        except sqlite3.Error as e:
            logger.error(f"Database error retrieving content key {content_key.to_string()}: {e}")
            return None
    
    def get_by_guid(self, guid: str) -> Optional[AnalysisResult]:
        """Retrieve analysis result by GUID."""
        conn = self._get_connection()
        
        try:
            # First find the content key hash via GUID mapping
            cursor = conn.execute(
                "SELECT content_key_hash FROM guid_aliases WHERE guid = ?",
                (guid,)
            )
            
            row = cursor.fetchone()
            if not row:
                return None
            
            content_key_hash = row['content_key_hash']
            
            # Now get the analysis result
            cursor = conn.execute(
                "SELECT * FROM analysis_cache WHERE content_key_hash = ?",
                (content_key_hash,)
            )
            
            analysis_row = cursor.fetchone()
            if not analysis_row:
                logger.warning(f"GUID {guid} maps to content key {content_key_hash} but no analysis found")
                return None
            
            # Update access tracking
            self._update_access_stats(content_key_hash)
            
            return self._row_to_analysis_result(analysis_row)
            
        except sqlite3.Error as e:
            logger.error(f"Database error retrieving GUID {guid}: {e}")
            return None
    
    def put(self, analysis_result: AnalysisResult) -> bool:
        """Store analysis result in persistent cache."""
        conn = self._get_connection()
        
        try:
            with conn:  # Transaction
                content_key = analysis_result.content_key
                content_key_hash = content_key.to_hash()
                
                # Insert or update analysis cache
                conn.execute(
                    """
                    INSERT OR REPLACE INTO analysis_cache (
                        content_key_hash, content_key_string, content_type, title, year, season, episode, quality,
                        decision, risk_score, probe_missing_ratio, file_count, data_segments, 
                        estimated_parity_blocks, age_days, poster, groups, size_bytes, indexer, original_title,
                        created_at, last_accessed, access_count, analysis_version, extended_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        content_key_hash,
                        content_key.to_string(),
                        content_key.content_type,
                        content_key.title,
                        content_key.year,
                        content_key.season,
                        content_key.episode,
                        content_key.quality,
                        analysis_result.decision,
                        analysis_result.risk_score,
                        analysis_result.probe_missing_ratio,
                        analysis_result.file_count,
                        analysis_result.data_segments,
                        analysis_result.estimated_parity_blocks,
                        analysis_result.age_days,
                        analysis_result.poster,
                        json.dumps(analysis_result.groups),
                        analysis_result.size_bytes,
                        analysis_result.indexer,
                        analysis_result.title,
                        analysis_result.timestamp,
                        analysis_result.timestamp,  # last_accessed = created_at initially
                        1,  # access_count
                        analysis_result.analysis_version,
                        json.dumps({
                            'sample_size': analysis_result.sample_size,
                            'probe_error_ratio': analysis_result.probe_error_ratio,
                            'risk_components': analysis_result.risk_components
                        })
                    )
                )
                
                # Add GUID mapping
                self._add_guid_mapping(conn, analysis_result.guid, content_key_hash, analysis_result.indexer)
                
            logger.debug(f"Stored analysis result for {content_key.to_string()}")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"Database error storing analysis result: {e}")
            return False
    
    def add_guid_alias(self, guid: str, content_key: ContentKey, indexer: str) -> bool:
        """Add a new GUID alias for existing content."""
        conn = self._get_connection()
        
        try:
            with conn:
                self._add_guid_mapping(conn, guid, content_key.to_hash(), indexer)
            
            logger.debug(f"Added GUID alias {guid} for {content_key.to_string()}")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"Database error adding GUID alias: {e}")
            return False
    
    def _add_guid_mapping(self, conn: sqlite3.Connection, guid: str, content_key_hash: str, indexer: str):
        """Add GUID mapping (internal method)."""
        conn.execute(
            "INSERT OR IGNORE INTO guid_aliases (guid, content_key_hash, indexer, created_at) VALUES (?, ?, ?, ?)",
            (guid, content_key_hash, indexer, time.time())
        )
    
    def _update_access_stats(self, content_key_hash: str):
        """Update access statistics for cache entry."""
        conn = self._get_connection()
        
        try:
            conn.execute(
                """
                UPDATE analysis_cache 
                SET last_accessed = ?, access_count = access_count + 1 
                WHERE content_key_hash = ?
                """,
                (time.time(), content_key_hash)
            )
            conn.commit()
        except sqlite3.Error as e:
            logger.warning(f"Failed to update access stats: {e}")
    
    def _row_to_analysis_result(self, row: sqlite3.Row) -> AnalysisResult:
        """Convert database row to AnalysisResult object."""
        # Parse extended data
        extended_data = json.loads(row['extended_data']) if row['extended_data'] else {}
        
        # Reconstruct content key
        content_key = ContentKey(
            content_type=row['content_type'],
            title=row['title'],
            year=row['year'],
            season=row['season'],
            episode=row['episode'],
            quality=row['quality']
        )
        
        return AnalysisResult(
            content_key=content_key,
            decision=row['decision'],
            risk_score=row['risk_score'],
            probe_missing_ratio=row['probe_missing_ratio'],
            file_count=row['file_count'],
            data_segments=row['data_segments'],
            estimated_parity_blocks=row['estimated_parity_blocks'],
            age_days=row['age_days'],
            poster=row['poster'],
            groups=json.loads(row['groups']) if row['groups'] else [],
            size_bytes=row['size_bytes'],
            indexer=row['indexer'],
            title=row['original_title'],
            guid="",  # GUID not stored in main table
            timestamp=row['created_at'],
            analysis_version=row['analysis_version'],
            sample_size=extended_data.get('sample_size'),
            probe_error_ratio=extended_data.get('probe_error_ratio'),
            risk_components=extended_data.get('risk_components')
        )
    
    def search_similar_content(self, content_key: ContentKey, size_tolerance: float = 0.1) -> List[AnalysisResult]:
        """Search for similar content based on heuristics."""
        conn = self._get_connection()
        
        try:
            # Build search query based on content type
            if content_key.content_type == 'movie':
                cursor = conn.execute(
                    """
                    SELECT * FROM analysis_cache 
                    WHERE content_type = 'movie' 
                    AND title = ? 
                    AND (year IS NULL OR year = ? OR ? IS NULL)
                    ORDER BY created_at DESC
                    """,
                    (content_key.title, content_key.year, content_key.year)
                )
            else:  # TV
                cursor = conn.execute(
                    """
                    SELECT * FROM analysis_cache 
                    WHERE content_type = 'tv' 
                    AND title = ? 
                    AND (season IS NULL OR season = ? OR ? IS NULL)
                    AND (episode IS NULL OR episode = ? OR ? IS NULL)
                    ORDER BY created_at DESC
                    """,
                    (content_key.title, content_key.season, content_key.season, 
                     content_key.episode, content_key.episode)
                )
            
            results = []
            for row in cursor.fetchall():
                results.append(self._row_to_analysis_result(row))
            
            return results
            
        except sqlite3.Error as e:
            logger.error(f"Database error searching similar content: {e}")
            return []
    
    def cleanup_expired(self) -> int:
        """Remove expired cache entries."""
        if self.max_age_days <= 0:
            return 0
        
        cutoff_time = time.time() - (self.max_age_days * 24 * 3600)
        conn = self._get_connection()
        
        try:
            with conn:
                # Get content keys to be deleted
                cursor = conn.execute(
                    "SELECT content_key_hash FROM analysis_cache WHERE created_at < ?",
                    (cutoff_time,)
                )
                expired_keys = [row[0] for row in cursor.fetchall()]
                
                # Delete GUID mappings first (foreign key constraint)
                conn.execute(
                    "DELETE FROM guid_aliases WHERE content_key_hash IN (SELECT content_key_hash FROM analysis_cache WHERE created_at < ?)",
                    (cutoff_time,)
                )
                
                # Delete analysis entries
                cursor = conn.execute(
                    "DELETE FROM analysis_cache WHERE created_at < ?",
                    (cutoff_time,)
                )
                
                deleted_count = cursor.rowcount
                logger.info(f"Cleaned up {deleted_count} expired cache entries")
                return deleted_count
                
        except sqlite3.Error as e:
            logger.error(f"Database error during cleanup: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        conn = self._get_connection()
        
        try:
            stats = {}
            
            # Total entries
            cursor = conn.execute("SELECT COUNT(*) FROM analysis_cache")
            stats['total_entries'] = cursor.fetchone()[0]
            
            # GUID mappings
            cursor = conn.execute("SELECT COUNT(*) FROM guid_aliases")
            stats['guid_mappings'] = cursor.fetchone()[0]
            
            # Content type breakdown
            cursor = conn.execute("SELECT content_type, COUNT(*) FROM analysis_cache GROUP BY content_type")
            stats['by_content_type'] = dict(cursor.fetchall())
            
            # Recent activity
            recent_cutoff = time.time() - (7 * 24 * 3600)  # Last 7 days
            cursor = conn.execute("SELECT COUNT(*) FROM analysis_cache WHERE last_accessed > ?", (recent_cutoff,))
            stats['recent_access_count'] = cursor.fetchone()[0]
            
            return stats
            
        except sqlite3.Error as e:
            logger.error(f"Database error getting stats: {e}")
            return {}
    
    def close(self):
        """Close database connections."""
        if hasattr(self._local, 'conn') and self._local.conn:
            self._local.conn.close()
            self._local.conn = None
