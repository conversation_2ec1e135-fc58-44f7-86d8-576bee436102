import sqlite3

conn = sqlite3.connect('workspace/preflight_cache/cache/analysis_cache.db')
cur = conn.cursor()

# Get table names
cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = [row[0] for row in cur.fetchall()]
print(f"Tables: {tables}")

# Check each table for row counts
for table in tables:
    cur.execute(f"SELECT COUNT(*) FROM {table}")
    count = cur.fetchone()[0]
    print(f"{table}: {count} rows")
    
    # Show a sample if there are rows
    if count > 0 and count <= 5:
        cur.execute(f"SELECT * FROM {table} LIMIT 3")
        rows = cur.fetchall()
        print(f"  Sample data: {rows}")

conn.close()