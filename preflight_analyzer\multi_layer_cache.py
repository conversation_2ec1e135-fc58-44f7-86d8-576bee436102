"""
Multi-Layer Cache Architecture - Main orchestrator for the robust cache system.

This module implements the unified cache interface that coordinates between:
- L1: In-memory cache (hot)
- L2: Persistent SQLite cache (warm) 
- GUID reconciliation and evolution tracking

This is the main entry point for all cache operations in the preflight analyzer.
"""

from __future__ import annotations
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .cache_models import <PERSON>Result, Content<PERSON>ey, AnalysisMode, parse_movie_content_key, parse_tv_content_key
from .memory_cache import MemoryCache
from .persistent_cache import PersistentCache
from .guid_reconciler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MatchCandidate
from .ttl_coordinator import get_ttl_coordinator

logger = logging.getLogger(__name__)


class MultiLayerCache:
    """
    Unified multi-layer cache interface.
    
    This class orchestrates the entire cache architecture:
    1. L1 (Memory): Fast in-memory LRU cache for hot data
    2. L2 (Persistent): SQLite-based durable cache for warm data
    3. GUID reconciliation: Content-based matching for evolving GUIDs
    """
    
    def __init__(self, 
                 cache_dir: Path,
                 memory_maxsize: int = 1000,
                 memory_ttl_seconds: int = 3600,
                 persistent_max_age_days: int = 365):
        """
        Initialize the multi-layer cache.
        
        Args:
            cache_dir: Directory for persistent cache storage
            memory_maxsize: Maximum entries in L1 memory cache
            memory_ttl_seconds: TTL for L1 cache entries
            persistent_max_age_days: Maximum age for L2 cache entries
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize cache layers
        self.memory_cache = MemoryCache(
            maxsize=memory_maxsize,
            ttl_seconds=memory_ttl_seconds
        )
        
        self.persistent_cache = PersistentCache(
            db_path=self.cache_dir / "analysis_cache.db",
            max_age_days=persistent_max_age_days
        )
        
        self.guid_reconciler = GuidReconciler()

        # Initialize TTL coordinator
        self.ttl_coordinator = get_ttl_coordinator()

        # Statistics
        self._stats = {
            'l1_hits': 0,
            'l2_hits': 0,
            'total_misses': 0,
            'guid_reconciliations': 0,
            'puts': 0
        }
        
        logger.info(f"Initialized multi-layer cache at {cache_dir}")
    
    def get_analysis(self,
                    guid: str,
                    title: str,
                    size_bytes: int,
                    indexer: str,
                    content_type: Optional[str] = None,
                    year: Optional[int] = None,
                    season: Optional[int] = None,
                    episode: Optional[int] = None,
                    analysis_mode: AnalysisMode = AnalysisMode.STANDARD) -> Optional[AnalysisResult]:
        """
        Retrieve analysis result using the multi-layer cache strategy.

        This is the main cache lookup method that:
        1. Checks L1 (memory) cache by GUID
        2. Checks L2 (persistent) cache by GUID
        3. If not found, attempts GUID reconciliation using content heuristics (mode-dependent)
        4. Updates L1 cache with any found results

        Args:
            guid: Release GUID
            title: Release title
            size_bytes: File size in bytes
            indexer: Indexer name
            content_type: 'movie' or 'tv' (auto-detected if None)
            year: Movie year
            season: TV season number
            episode: TV episode number
            analysis_mode: Controls deduplication behavior (standard/reliability/hybrid)

        Returns:
            AnalysisResult if found in cache, None otherwise
        """
        # Step 1: Check L1 memory cache by GUID
        result = self.memory_cache.get_by_guid(guid)
        if result:
            self._stats['l1_hits'] += 1
            logger.debug(f"L1 cache hit for GUID: {guid}")
            return result
        
        # Step 2: Check L2 persistent cache by GUID
        result = self.persistent_cache.get_by_guid(guid)
        if result:
            self._stats['l2_hits'] += 1
            # Populate L1 cache for future requests
            self.memory_cache.put(result)
            logger.debug(f"L2 cache hit for GUID: {guid}")
            return result
        
        # Step 3: GUID not found - attempt reconciliation (mode-dependent)
        logger.debug(f"GUID {guid} not found in cache, attempting reconciliation (mode: {analysis_mode.value})")

        # In RELIABILITY mode, skip reconciliation entirely - force fresh analysis
        if analysis_mode == AnalysisMode.RELIABILITY:
            self._stats['misses'] += 1
            logger.debug(f"RELIABILITY mode: skipping reconciliation for GUID {guid}")
            return None

        # Extract content metadata for matching
        metadata = self.guid_reconciler.extract_content_metadata(title, guid, indexer)
        if content_type:
            metadata['content_type'] = content_type

        # Override with provided parameters
        if year is not None:
            metadata['year'] = year
        if season is not None:
            metadata['season'] = season
        if episode is not None:
            metadata['episode'] = episode

        # Create content key for searching
        content_key = self._create_content_key_from_metadata(metadata)
        
        # Check L1 cache by content key (STANDARD and HYBRID modes)
        result = self.memory_cache.get_by_content_key(content_key)
        if result:
            # In HYBRID mode, we found cached metadata but may need fresh reliability checks
            if analysis_mode == AnalysisMode.HYBRID:
                logger.debug(f"HYBRID mode: found cached metadata for {content_key.to_string()}, "
                           f"but reliability checks may be needed")
                # For now, return the cached result - reliability checks would be handled
                # by the calling code based on the analysis_mode

            self._stats['l1_hits'] += 1
            # Add GUID alias to memory cache
            self.memory_cache.put_with_guid_alias(guid, result)
            # Add GUID alias to persistent cache
            self.persistent_cache.add_guid_alias(guid, content_key, indexer)
            self._stats['guid_reconciliations'] += 1
            logger.info(f"L1 content key reconciliation: {guid} -> {content_key.to_string()}")
            return result

        # Check L2 cache by content key (STANDARD and HYBRID modes)
        result = self.persistent_cache.get_by_content_key(content_key)
        if result:
            # In HYBRID mode, we found cached metadata but may need fresh reliability checks
            if analysis_mode == AnalysisMode.HYBRID:
                logger.debug(f"HYBRID mode: found cached metadata for {content_key.to_string()}, "
                           f"but reliability checks may be needed")

            self._stats['l2_hits'] += 1
            # Add to L1 cache
            self.memory_cache.put_with_guid_alias(guid, result)
            # Add GUID alias to persistent cache
            self.persistent_cache.add_guid_alias(guid, content_key, indexer)
            self._stats['guid_reconciliations'] += 1
            logger.info(f"L2 content key reconciliation: {guid} -> {content_key.to_string()}")
            return result
        
        # Step 4: Search for similar content using heuristics
        similar_results = self.persistent_cache.search_similar_content(content_key)
        if similar_results:
            match_candidate = self.guid_reconciler.find_matching_content(
                new_guid=guid,
                new_title=title,
                new_size=size_bytes,
                new_indexer=indexer,
                content_type=metadata['content_type'],
                existing_results=similar_results,
                year=metadata.get('year'),
                season=metadata.get('season'),
                episode=metadata.get('episode')
            )
            
            if match_candidate:
                result = match_candidate.analysis_result
                # Add GUID alias
                self.memory_cache.put_with_guid_alias(guid, result)
                self.persistent_cache.add_guid_alias(guid, result.content_key, indexer)
                self._stats['guid_reconciliations'] += 1
                logger.info(f"Heuristic reconciliation: {guid} -> {result.content_key.to_string()} "
                           f"(confidence: {match_candidate.confidence_score:.3f})")
                return result
        
        # Cache miss - no matching content found
        self._stats['total_misses'] += 1
        logger.debug(f"Cache miss for GUID: {guid}")
        return None
    
    def put_analysis(self, analysis_result: AnalysisResult, analysis_mode: AnalysisMode = AnalysisMode.STANDARD) -> bool:
        """
        Store analysis result in the multi-layer cache.

        Uses write-through caching: stores in both L1 and L2 immediately.
        In RELIABILITY mode, each GUID gets its own entry even if content is similar.

        Args:
            analysis_result: The analysis result to store
            analysis_mode: Controls how the result is stored (affects deduplication)

        Returns:
            True if stored successfully, False otherwise
        """
        try:
            # Store in L2 persistent cache first (source of truth)
            success = self.persistent_cache.put(analysis_result)
            if not success:
                logger.error(f"Failed to store analysis result in persistent cache")
                return False
            
            # Store in L1 memory cache
            self.memory_cache.put(analysis_result)
            
            self._stats['puts'] += 1
            logger.debug(f"Stored analysis result: {analysis_result.content_key.to_string()}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing analysis result: {e}")
            return False
    
    def invalidate_guid(self, guid: str) -> bool:
        """Invalidate cache entries for a specific GUID."""
        l1_removed = self.memory_cache.invalidate_guid(guid)
        # Note: We don't remove from L2 as it's meant to be persistent
        # But we could add a flag to mark entries as invalid if needed
        return l1_removed
    
    def invalidate_content_key(self, content_key: ContentKey) -> bool:
        """Invalidate cache entries for a specific content key."""
        l1_removed = self.memory_cache.invalidate_content_key(content_key)
        return l1_removed
    
    def clear_memory_cache(self):
        """Clear the L1 memory cache."""
        self.memory_cache.clear()
        logger.info("Cleared L1 memory cache")
    
    def cleanup_expired(self) -> int:
        """Clean up expired entries from persistent cache."""
        return self.persistent_cache.cleanup_expired()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        l1_stats = self.memory_cache.get_stats()
        l2_stats = self.persistent_cache.get_stats()
        
        total_requests = self._stats['l1_hits'] + self._stats['l2_hits'] + self._stats['total_misses']
        
        return {
            'requests': {
                'total': total_requests,
                'l1_hits': self._stats['l1_hits'],
                'l2_hits': self._stats['l2_hits'],
                'total_hits': self._stats['l1_hits'] + self._stats['l2_hits'],
                'misses': self._stats['total_misses'],
                'hit_rate': (self._stats['l1_hits'] + self._stats['l2_hits']) / total_requests if total_requests > 0 else 0.0,
                'guid_reconciliations': self._stats['guid_reconciliations'],
                'puts': self._stats['puts']
            },
            'l1_memory': l1_stats,
            'l2_persistent': l2_stats
        }
    
    def get_cache_info(self) -> str:
        """Get human-readable cache information."""
        stats = self.get_cache_stats()
        req_stats = stats['requests']
        
        return (
            f"Multi-Layer Cache Stats: "
            f"hit_rate={req_stats['hit_rate']:.1%} "
            f"(L1: {req_stats['l1_hits']}, L2: {req_stats['l2_hits']}, misses: {req_stats['misses']}), "
            f"reconciliations={req_stats['guid_reconciliations']}, "
            f"L2_entries={stats['l2_persistent'].get('total_entries', 0)}"
        )
    
    def _create_content_key_from_metadata(self, metadata: Dict[str, Any]) -> ContentKey:
        """Create ContentKey from extracted metadata."""
        if metadata['content_type'] == 'movie':
            return parse_movie_content_key(
                title=metadata.get('series_title', metadata['title']),
                year=metadata.get('year'),
                quality=metadata.get('quality')
            )
        else:  # TV
            return parse_tv_content_key(
                title=metadata.get('series_title', metadata['title']),
                season=metadata.get('season'),
                episode=metadata.get('episode'),
                quality=metadata.get('quality')
            )
    
    def close(self):
        """Close cache connections and cleanup resources."""
        self.persistent_cache.close()
        logger.info("Closed multi-layer cache")
