Unified Cache-Aware TV Show Preflight Analysis System (Design Proposal)
Introduction

We propose a unified preflight analysis module for TV shows that consolidates all pre-download decision logic into a single tv_show_preflight_selector.py. This module will mirror the design of the existing movie preflight selector, but accommodate TV-specific complexities (multiple episodes, season packs, etc.). The goal is to improve architectural clarity and maintainability, while preserving performance, caching benefits, and integration with existing workflows (e.g. Sonarr). Key features of this design include a single entry-point function, support for multiple analysis modes (standard, reliability, hybrid), intelligent handling of season packs vs individual episodes, deep integration with a multi-layer caching system, and robust observability (logging/metrics) for cache and analysis behavior.

Single Entry Point: preflight_tv_show()

All TV preflight logic will be accessed via one central function: preflight_tv_show(). This function serves as the sole entry point for analyzing TV show releases before download, replacing fragmented or duplicated logic. It will:

Accept parameters identifying the TV content to analyze (e.g. series ID or name, specific episode identifiers or a list of episodes, etc.), along with an analysis_mode flag.

Internally orchestrate the entire workflow – from determining which episodes or packs to evaluate, performing the analysis (with caching), to returning decisions or triggering downloads.

Encapsulate all TV-specific decision branches within this function (or its helpers), so that external components have a single interface to call for any TV preflight operation. This mirrors how movie_preflight_selector.py offers one clear entry-point for movies, making the system consistent.

Function Signature (Proposed):

def preflight_tv_show(series_id: int, episode_ids: List[int], 
                      mode: str = "standard", callback: Optional[Callable] = None) -> Dict:
    ...


series_id (or series name) identifies the show; episode_ids could be one or multiple episodes to analyze (for example, Sonarr might pass a list of missing episodes or a whole season).

mode selects the analysis strategy (details below).

callback is an optional function to call after analysis to trigger actual downloading.

The function will return a structured result (e.g. a dict or object containing decisions for each episode or a combined decision for a pack) consistent with existing expectations.

Having a single entry-point makes the system easier to invoke and test. All upstream code (Sonarr integration, CLI tools, etc.) can call preflight_tv_show() regardless of context (single episode grab or full-season analysis). Internally, this function will delegate to helper methods as needed, but those details are hidden from the caller, resulting in a clean API.

Support for Multiple Analysis Modes

The system will support three analysis modes – standard, reliability, and hybrid – to control caching and decision behavior. The analysis_mode parameter (likely a string or enum) will adjust how preflight analysis is performed:

Standard Mode: This is the default mode optimized for routine operations. It emphasizes speed by using cached decisions and avoiding redundant work. In standard mode, the system will utilize cached analysis results when available and deduplicate work via GUID reconciliation (i.e. if the same release covers multiple episodes or was seen before, reuse that decision). For example, if episode 1 and 2 are part of the same pack (same unique GUID), standard mode will analyze it once, cache the result, and reuse it for both episodes. This mode maintains the current “hybrid decision-making” that exists in the legacy logic – meaning it can intelligently choose between individual episodes vs packs using existing heuristics (more on that below).

Reliability Mode: This mode prioritizes thoroughness over speed. It will force fresh analysis for every episode and pack, ignoring any cached decisions and bypassing deduplication. Every piece of content is treated as unique even if it was seen before, ensuring that we catch any issues that might have been missed or any changes since the last analysis. In reliability mode, if a season pack contains 10 episodes, the system will effectively analyze each episode in that pack individually (simulated unpacking) to gather granular telemetry and verify each file. No shortcuts are taken – caches may still be updated for record-keeping, but not trusted for skipping analysis. This mode is ideal for final verification passes or situations where maximum accuracy is required.

Hybrid Mode: This mode offers a balance between the two extremes. The system will reuse cached metadata and results where appropriate, but still perform fresh “reliability” checks on critical aspects of each release. In practice, hybrid mode might load a cached analysis result (to reuse things like previously computed video quality, file list, etc.), but then do a fresh minimal download or integrity check to ensure the release is still good (for example, re-check that the torrent has sufficient seeders or that no new corruption is detected). It avoids full re-analysis from scratch if not needed, but doesn’t fully trust the cache for things that can change over time (like availability). Hybrid mode thus reuses what’s safe to cache and re-validates what isn’t – giving improved performance over pure reliability mode, with more confidence than standard mode. In standard operation, this hybrid strategy is already partly employed (e.g. the system might reuse metadata but update certain probes), and we will maintain that logic explicitly in this mode.

Implementation Strategy: The preflight_tv_show function will interpret the mode and set up analysis accordingly. For example, it may have logic like:

if mode == "standard":
    use_cache = True
    deduplicate = True
elif mode == "reliability":
    use_cache = False  # always do fresh
    deduplicate = False
elif mode == "hybrid":
    use_cache = True   # reuse cache results...
    deduplicate = True
    fresh_checks = True  # ...but perform certain checks anew


These flags would then govern the flow of analysis (cache usage, skipping logic, etc.). The outcome is that all three modes are handled through the same unified code paths, with conditional branches where needed to alter behavior. This makes the system flexible: operators can choose reliability mode for one-off deep checks, or standard for everyday use, without changing the underlying pipeline.

Hybrid Strategy for Episodes vs. Season Packs

A core complexity in TV workflows is deciding between downloading individual episodes or a season pack (a batch containing multiple episodes). The unified preflight system will incorporate the existing “hybrid” strategy that handles this decision, ensuring we don’t regress any current behavior:

In standard (and hybrid) mode, the logic from the old integrated_selector for choosing packs vs episodes will be maintained. Typically, this means if multiple episodes are requested (e.g. backlog of a season), the system will check if a full season pack is available and suitable. If a good pack is found, it may be more efficient to download one pack instead of many separate files. Conversely, if episodes are being acquired one-by-one (e.g. weekly airing), the system would stick to single-episode downloads. The preflight_tv_show function will encapsulate this decision-making:

It will use the pack_search utility (or Sonarr’s API) to search for a season pack when a batch of episodes is being processed. For example, if the user/sonarr triggers a search for “Season 1”, we query indexers for a release that includes all of Season 1.

If a pack is found, we include it in the analysis candidates. We’ll analyze the pack (using caching if possible) alongside or before analyzing individual episodes.

Based on the analysis results, the module can then decide: if the season pack is accepted (high quality, complete, low risk) and covers all episodes, we can favor it and skip individual episode downloads. If the pack is rejected (e.g. it’s incomplete or poor quality) or missing episodes, then we fall back to individual episode releases. This logic ensures we always choose the best option (pack vs episodes) given the data.

We will preserve any thresholds or policies from the legacy system (for instance, maybe it only prefers packs if a certain number of episodes are missing, etc.), to maintain consistent behavior in standard mode.

In reliability mode, the system will not short-cut any path. It will analyze every episode and pack fully and individually. This means even if a pack is found, the system might also analyze the episodes individually (and not skip them due to deduplication). The reasoning is to gather complete data on each approach:

The phrase “simulate per-episode unpacking” means that for a season pack, reliability mode will treat it as if it were broken into individual episodes for analysis. In practice, if the pack contains multiple files (one per episode), the system will iterate through each file and run the usual analysis on each (e.g. verify file integrity, length, etc. for episode 1 file, then episode 2 file, etc.). This yields granular telemetry like per-episode completeness and quality, rather than one aggregated result. If the pack is a single file that has multiple episodes merged, the analysis will still attempt to verify the content covers the entire duration of both episodes.

By also analyzing standalone episode releases in this mode, we could compare the outcomes. For example, perhaps the pack was complete but lower quality, whereas individual episodes are higher quality – reliability mode ensures we have both data points. Ultimately, the system can then choose the truly best option having fully vetted each. (It’s possible in reliability mode the system might end up downloading both for comparison in edge cases, but generally it will still pick one path – the full analysis just gives confidence in that choice.)

Maintaining Hybrid Logic: In standard mode, we keep the existing “hybrid” decision process that might, for instance, take a middle-ground: download weekly episodes normally, but if a season pack appears that has all remaining episodes at good quality, use it for batch replacement. Our unified module will retain this nuanced behavior by embedding those rules. Essentially, the design doesn’t remove any smart logic – it consolidates it so that all such decisions happen in one place (inside preflight_tv_show) rather than being scattered.

Example Flow: Suppose episodes 5–10 of a season are missing. In standard mode, preflight_tv_show sees multiple episodes requested and calls pack_search for a Season 1 pack:

If a Season 1 pack is found, analyze it (cache-enabled). Say it’s complete and acceptable. The system logs a decision like “Season pack found and accepted for S01E05–E10” and will use that single pack to satisfy all those episodes (downloading it once). The individual episode searches might either be skipped or if they were fetched, their results will be disregarded in favor of the pack (and possibly cached for future reference).

If no pack is found or the pack is bad (e.g. it’s a known incomplete), then the system proceeds to analyze episodes 5–10 individually (possibly in parallel). It might still check if any release covers multiple episodes (like a double-episode file) and handle that accordingly (see next section). After analysis, it will trigger downloads for each acceptable episode release.
In reliability mode, by contrast, the system would likely analyze the pack and the episodes 5–10, log everything, and then perhaps choose one route based on which had better results (with a bias towards thoroughness).

The unified approach ensures that whether we deal with packs or episodes, the logic is centralized, making it easier to adjust strategy in the future (for example, if we want to change how we prefer packs, there’s one place to do it).

TV-Specific Features & Special Cases

Multi-Episode Release Detection: TV releases sometimes contain more than one episode in a single file (common for double-premieres or finale parts). Our system will include logic to detect these cases from release naming or metadata. For example, a torrent named “ShowName S01E01E02 …” or “... S01E01-02 ...” indicates it includes episodes 1 and 2
forums.sonarr.tv
forums.sonarr.tv
. We will:

Parse release titles using regex or existing parsing utilities (possibly leveraging Sonarr’s parsing logic or our own in pack_search) to identify multi-episode patterns (e.g. S01E01-02 or 1x01x02).

When a multi-episode file is found, treat it specially in analysis:

Standard mode: One analysis task will cover both episodes. The resulting decision (and cache entry) should be linked to both episode identifiers so that we don’t double-analyze or double-download. For instance, if “S01E01E02” file is analyzed and accepted, we mark episode 1 and 2 as satisfied by that single file. The caching system’s content key can reflect the combined episodes, so a future request for E01 or E02 recognizes that file (GUID) as already analyzed. This prevents duplicate work and ensures consistent decisions for both episodes.

Reliability mode: If the multi-episode release actually contains two separate files (inside a pack), they’ll be handled individually anyway. If it’s truly one file that is two episodes back-to-back, the analysis will likely verify the full runtime covers both episodes (e.g. checking the video length or chapters). We might log a single analysis result but tag it for two episodes. Additionally, we ensure the system doesn’t erroneously try to download another release for the second episode – it knows they’re together.

This detection helps avoid edge cases where, say, Sonarr might send two episode requests but they’re actually fulfilled by one download. Our unified module will reconcile that by recognizing the multi-episode release and consolidating the decision.

Season Pack Search & Fallback: As mentioned, the system will actively search for season packs when appropriate. The design will likely include a helper call such as:

pack_release = pack_search.find_best_pack(series_id, season_number)


If a pack is found, it’s included in the analysis pipeline. Important details:

We’ll incorporate an optional fallback logic: if a season pack is not found or is not chosen (e.g. due to quality or issues), the system seamlessly falls back to per-episode downloads. This means users get the best of both worlds – one large download if possible, otherwise individual ones – without manual intervention.

The pack search may use criteria like ensuring the pack’s quality matches the profile (e.g. 1080p WEBRip pack if that’s desired) and that it includes all monitored episodes. If the pack is missing an episode or two, the system could decide to reject it or partially use it (though mixing pack + singles in one season is usually not ideal; the logic may simply reject packs that aren’t complete).

Efficiency: By checking for a pack first, we can avoid spinning up analysis for, say, 20 separate episodes if one pack can cover them all. This is especially useful for older seasons where packs are common. The unified module’s structured approach ensures this check happens early in the workflow for batch requests.

Post-Analysis Callback Support: A notable enhancement is making the final download trigger more flexible via a callback. Instead of the preflight logic directly invoking download actions within itself, it will support a callback mechanism:

The callback (if provided to preflight_tv_show) will be called after the analysis phase for each item that is approved for download. This decouples the analysis from the action of downloading. For example, in a Sonarr integration context, the callback might be a function that tells Sonarr to accept the release or sends the torrent to a downloader. In a testing context, the callback could simply log the selection without actually downloading.

The callback interface could be something like callback(series_id, episode_id_or_list, release_info) where release_info includes details like the GUID or download link. For a season pack, we might call callback(series_id, [episode_list], pack_release), whereas for individual episodes it might be one call per episode. This design ensures that even if one pack covers multiple episodes, we handle it correctly (perhaps one callback call for the pack with multiple episodes indicated, so the client knows one download satisfies all).

By supporting a callback, we achieve separation of concerns: tv_show_preflight_selector.py focuses on deciding what to download, and the callback (provided by outside code) handles how to initiate the download. This makes the core module easier to maintain and test (we can run it without actually downloading anything by giving a dummy callback). It also aligns with a likely pattern in the movie preflight selector (ensuring consistency in how downloads are triggered post-analysis).

In summary, these TV-specific features ensure the new module robustly handles the unique scenarios in TV workflows (multiple episodes in one release, bulk season handling, etc.), providing both intelligence and flexibility that match or exceed the current system.

Cache Architecture and Content-Aware Caching

A cornerstone of the new design is deep integration with a multi-layer cache for decisions, to improve performance and avoid redundant analyses. We will leverage the existing DecisionCache class (backed by multi_layer_cache.py) to store and retrieve analysis results:

DecisionCache Integration: The DecisionCache provides a high-level interface to the caching system, maintaining the same API as the old DecisionHistory (for compatibility) but using a robust multi-layer backend. We will initialize a single DecisionCache instance (likely at module load or when first needed) pointing to the cache storage (e.g., a persistent JSON or SQLite DB used by the old system). This instance will be shared by all analyses in the TV module. By using DecisionCache, we automatically get:

A memory cache (L1) for hot, recent decisions (to avoid disk I/O on repeated queries).

A persistent cache (L2), e.g. SQLite or file-based, for storing decisions across restarts (so we remember analysis done days or weeks ago).

GUID reconciliation logic, which can identify content by attributes even if the GUID (e.g. indexer ID or torrent hash) differs. For instance, if the same exact video file is uploaded to two different trackers (hence two GUIDs), the cache can recognize they are the same content and return the cached result for either GUID. This is achieved via content-based keys under the hood.

Transparent caching API: we call decision_cache.get(key) to retrieve a cached result and decision_cache.put(key, decision, report) to store one, without worrying about which layer it’s in – the MultiLayerCache handles that.

Content-Aware Cache Keys: To properly support TV scenarios, cache keys must uniquely identify content, including whether it’s an individual episode or a pack:

We will use the convention "Indexer:GUID" as the primary cache key (as was used in DecisionHistory). For example, a Usenet indexer might provide a GUID or a torrent hash can serve as GUID. The DecisionCache will parse this and perform lookups. If the GUID is found in cache, we get a hit. If not, the MultiLayerCache may fall back to a content-based search.

The content-based key (handled by ContentKey and the GuidReconciler internally) includes details such as the title (sans episode info), season/episode numbers for TV, and quality. For instance, an episode release might generate a content key like “ShowName|S1E5|1080p WEB-DL”, whereas a season pack might be “ShowName|S1|PACK|1080p”. These keys allow the cache to detect when two different GUIDs actually refer to the same underlying content.

Season Pack vs Episode: The cache will distinguish them via the content key. A season pack covering episodes 1-10 will have a key indicating “season 1, episodes 1-10” (the exact representation depends on implementation). Thus it won’t collide with a single-episode entry. Moreover, if an episode is later seen individually, the cache can know it’s contained in the pack’s content key and potentially reconcile (depending on implementation). This content-aware design is crucial for preventing duplicate analysis: e.g., analyze a season pack once, and the next time an individual episode from that season appears, the cache can return the pack’s analysis result (if appropriate) instead of redoing it. In DecisionCache’s new architecture, this is one of the improvements for performance and GUID matching.

The DecisionCache.put() logic will extract the needed metadata from the analysis report to form these content keys before storing. Our preflight code just provides the raw info (title, season, episode, quality, etc.) in the report, and the cache layer handles key creation and storage in both L1 and L2.

Cache Observability: We will make full use of CacheLogger and CacheMetrics provided by the cache system for transparency:

Each cache access (get/put) will be logged via CacheLogger. For example, when decision_cache.get() hits an entry, it logs a cache hit event with details (GUID, content key, latency). On a miss or error, it logs those as well. We will ensure these logs propagate to our module’s logging or are accessible for debugging cache behavior.

CacheMetrics will be incremented accordingly on hits, misses, puts, etc. This allows us to gather stats such as hit rate, number of entries, evictions, etc. (for instance, how often are we saving time by using the cache?). The DecisionCache/MultiLayerCache has methods to get stats and reports, which we can use for monitoring or during shutdown to report cache performance. We may periodically call decision_cache.get_stats() or use its health_monitor to ensure the cache is functioning well.

By integrating these, the new module will have cache observability built-in: if a developer or admin needs to see if the cache is effective or if GUID reconciliation is happening, the data is readily available (e.g., a log might show “Cache hit for GUID XYZ (mapped to content ShowName S1E5)” which confirms deduplication is working).

Using the Cache in Analysis Workflow: The typical pattern in our analysis flow will be:

Construct the cache key (like "IndexerName:GUID"). This info comes from the release data we’re analyzing (e.g., provided by Sonarr or the indexer result).

If use_cache is allowed (standard/hybrid mode), call decision_cache.get(key).

If it returns a result (cache hit), we interpret that result: it likely contains decision (e.g. "ACCEPT" or "REJECT_INCOMPLETE"), risk score, and missing_ratio (and possibly a timestamp). We log that we got a cached decision and skip the heavy analysis steps for this release. We will still consider mode: in pure standard mode we might trust it fully; in hybrid mode, we might use it but still do a light check (as described). Regardless, using the cache can short-circuit a lot of work.

If it’s a miss, we proceed to analyze the content from scratch.

After analyzing (or if we had a cached result but perhaps updated something), we call decision_cache.put(key, decision, report) to store the new or updated analysis outcome. The report is a dictionary containing detailed analysis info (file count, sizes, any metrics like probe_missing_ratio, etc.), and decision is our final verdict for that release. The DecisionCache will convert this into an AnalysisResult object and commit it to the multi-layer cache (both memory and persistent store). It logs a cache put event with latency and content key. This means next time we encounter the same content, we can retrieve this without redoing the analysis work.

Cache TTL and Refresh: By default, the in-memory cache has a TTL (e.g. 12 hours as in DecisionCache init), and persistent cache retains much longer (365 days). This strikes a balance between using recent results and not keeping very old results indefinitely (to avoid stale info). If in hybrid mode we suspect some cached info might be stale (say a result from weeks ago), we could optionally force a re-check despite the cache. The system can also expose a way to invalidate cache entries when needed (the MultiLayerCache supports invalidation by GUID or content key, which we could call e.g. if a pack was found to be bad and we want to avoid reusing it).

In summary, integrating the DecisionCache and underlying multi-layer architecture brings significant performance gains and robust behavior to our TV preflight analysis. We avoid duplicate analyses of the same release across episodes or runs, capitalize on past work (especially important for large season packs that are time-consuming to analyze), and get a scalable cache that can handle lots of data efficiently. All of this is done while maintaining transparency (through logging/metrics) and consistency (the DecisionCache preserves the old interface, so other parts of the system that used DecisionHistory will continue to work without changes).

Modular Design and Refactoring Plan

To build this unified system cleanly, we will refactor and organize the code into clear modules with separated concerns. The goal is to absorb the TV logic from integrated_selector.py into tv_show_preflight_selector.py, while keeping related utilities in their own modules. The proposed structure and responsibilities are:

tv_show_preflight_selector.py: This new module is the core orchestrator for TV preflight analysis. It contains:

The preflight_tv_show() function (entry point) and possibly a small class or namedtuple for returning results (e.g., TvPreflightResult if needed for clarity).

Internal helper functions described earlier, like analyze_release() or specialized ones (_analyze_pack_contents, _analyze_episode_release, etc.), to break down tasks.

Minimal direct external calls – instead, delegate to utility modules for specific tasks (for example, do not embed Sonarr API calls or search queries directly here; call sonarr_client or pack_search functions).

No overlapping logic with movies – movie-specific code stays in movie_preflight_selector.py. If there is common functionality (like some generic cache handling or similar), we could factor it into a shared helper or simply duplicate small bits, depending on what’s cleaner. The key is that TV and movie flows are independent but analogous.

Elimination of integrated_selector.py: Previously, if integrated_selector was a combined flow for both movies and TV, we will decompose it:

The movie parts should already be handled by movie_preflight_selector.py. The TV parts will now reside in tv_show_preflight_selector.py.

If integrated_selector.py served as a routing module (detecting content type and calling appropriate logic), we can simplify it to just dispatch: e.g. if content type is “movie”, call movie_preflight; if “series”, call tv_preflight. Eventually, we might retire this file entirely and have the callers invoke the correct preflight function directly.

By removing integrated logic, we avoid duplication and potential inconsistencies. For example, any fixes or improvements to TV logic now happen in one place (tv_show_preflight_selector) instead of having to mirror changes in an integrated flow.

Reuse of Utilities: We will retain and use existing helper modules for specific subtasks, rather than reimplement them:

sonarr_client.py: This module likely provides functions to interact with Sonarr’s API or data structures (e.g., fetching series details, marking releases, getting profile info). The preflight selector will call these functions as needed. For instance, it might use sonarr_client.get_series_info(series_id) to get series title (for logging) or episode list, or use sonarr_client.notify_release_decision(...) if Sonarr needs to be informed of the decision outcome. Keeping this separate means our module doesn’t need to know API details – it just asks for what it needs.

pack_search.py: This contains logic to search indexers for packs (and possibly to rank them). The preflight module will use something like pack_search.search_for_season(series_title, season_number) which returns candidate releases for the season pack. It might also have logic for multi-episode releases (some overlap in parsing). By calling into pack_search, we ensure the search and parsing logic is centralized. If tomorrow the way we search for packs changes (say new indexer API), we update pack_search.py without touching the preflight module.

Other utility modules might include things like quality_filter.py or video_probe.py (if they exist for analyzing video files). We will continue to use those for the actual content analysis. For example, after downloading a sample of a video, we might call video_probe.analyze(file_path) to get details on missing segments or verify the encode. The preflight module then interprets those results.

Separation of Concerns: Each module has a clear role:

Preflight selector: orchestration and decision logic.

Sonarr client: external communication.

Pack search: finding content to analyze.

Cache: storing/retrieving past decisions.

Analysis functions: actual inspection of files/metadata.
This separation not only makes the code easier to reason about, but also easier to test (each piece can be unit-tested with mocks for the others).

API Structure Consistency: We will ensure that the functions and classes we expose follow a consistent style with the movie preflight system. For instance:

If movie_preflight_selector.preflight_movie(movie, mode) returns a tuple of (decision, report), then preflight_tv_show might return a similar structure or a list of such tuples for episodes. We’ll define a clear return type. Possibly, we might define a data class EpisodeDecision with fields like episode_id, decision, reason, etc., and return a list of those for all processed episodes (or one for a pack covering multiple episodes). However, since the user specifically notes “preserve current return formats”, it implies the new function should output the same kind of data the old system did – likely to not break any calling code. If previously the integrated selector updated a global state or produced a JSON, we mimic that in the new function.

The naming and behavior of preflight_tv_show should align with preflight_movie. Perhaps in the future, both could be methods of a class or follow a similar pattern (like analyzer.preflight(content)). But for now, matching the design is sufficient.

Consistency also means the new module should handle errors/exceptions similarly to the movie version (e.g., how does it signal a failure? throw exception or return a special decision?). We’ll follow the movie selector’s pattern to keep usage uniform.

Maintaining Legacy Hooks: Because we are refactoring, we will include any needed adapter code so that legacy components continue to function:

For example, if other parts of the system call a now-deprecated integrated_selector.select_show(...), we can implement that function to simply call preflight_tv_show() internally and return its result. This way we don’t have to hunt down and change all call sites at once – they can be migrated gradually.

The DecisionHistory vs DecisionCache change has already been handled by providing DecisionHistory = DecisionCache alias, meaning old code referencing DecisionHistory is automatically using the new cache. We’ll ensure that in our TV module, if it previously used a DecisionHistory object, we instantiate DecisionCache but can still call it decision_history in code for consistency. Over time we can rename it, but initially this avoids confusion.

The net effect of this modular refactoring is a clean architecture where each piece of functionality is in the right place. The tv_show_preflight_selector.py becomes the clear center of TV logic, making it easier for developers to add features (like a new analysis check) without touching unrelated parts. Removing the integrated approach eliminates duplicated code and potential divergence between how movies and shows were handled. Now, both have their dedicated modules designed in parallel, which improves maintainability.

Concurrency and Performance Considerations

TV show analysis often needs to handle multiple items at once (e.g., checking 10 episodes together). Our design will preserve and enhance the parallel processing capabilities to keep performance high:

Parallel Analysis of Episodes: The system will analyze different episodes concurrently whenever possible. For instance, if we have 5 episode files to check (or 5 different releases), we can spawn parallel tasks for each rather than doing them sequentially. In Python, this could be done with an asyncio event loop (making preflight_tv_show an async function and using await/gather on coroutines for each analysis) or using threads via concurrent.futures.ThreadPoolExecutor if the analysis involves blocking I/O. The choice may depend on how the existing system is built (if movie preflight uses asyncio, we’ll follow that pattern).

We will be cautious to not spawn too many threads – perhaps use a thread pool sized to the number of CPU cores or a configured limit to avoid overload. If using asyncio, many tasks can be scheduled as long as the actual I/O is non-blocking.

When a season pack is being analyzed and individual episodes are also to be analyzed (like in reliability mode), we might run those in parallel too. However, if they involve downloading the same large file twice, that’s inefficient. Instead, the design could allow sharing the data: e.g., download the pack once, but process it in multiple threads (one per contained episode) to simulate separate analyses. This way reliability mode gets per-episode checks without re-downloading data repeatedly. Such optimizations can be considered in implementation.

The concurrency extends to any external API calls as well – for example, if we need to query multiple indexers or run multiple pack searches, we will do so asynchronously.

Asynchronous Workflows: We intend to maintain an asynchronous design for non-blocking operations:

If Sonarr triggers a search for multiple episodes, preflight_tv_show could kick off all necessary analyses and then yield control while they complete, rather than blocking the main thread. This would keep the application responsive and able to handle other tasks simultaneously.

The use of async def for the function and await on I/O (like file reading or network requests) can achieve concurrency within a single thread. For CPU-bound tasks (like hashing a file), offloading to a thread executor is possible to avoid stalling the event loop.

This approach aligns with how a high-performance server might operate, and ensures that even if one analysis is waiting on a slow disk read or network response, others can proceed.

Parallelism for Packs: Season packs (if they contain many files) present an opportunity for internal parallelism as well:

In reliability mode, since we’re effectively analyzing each episode file in the pack, we can analyze different files in parallel. For example, a pack with 10 episode files could have 3 threads checking different files concurrently to speed up the overall pack verification. The system would aggregate the results. This will be implemented carefully to not thrash the disk (maybe limit concurrency within a pack).

In standard mode, we typically wouldn’t analyze each file individually (to save time), so this is less relevant there.

Performance Optimization via Caching: The caching integration itself is a huge performance booster:

Cache hits mean we skip heavy operations (like not needing to re-download a sample or re-parse a file) and just trust the stored result, which is extremely fast (memory lookup or single DB query). The multi-layer cache is designed for speed, using in-memory LRU for frequent items.

GUID reconciliation further improves performance by broadening cache hits – e.g., even if the exact GUID isn’t seen, we might catch it via content key if we saw the same content via another path. This reduces duplicate analysis across different sources.

We will keep track of how often we’re hitting vs missing to gauge effectiveness. A high hit rate means the system is avoiding a lot of work (which is good).

Non-Blocking Cache Calls: Accessing the cache (especially memory cache) is very fast and effectively non-blocking. Even writing to the persistent cache (SQLite) is usually quick, but we might do it asynchronously if needed. If high throughput is expected (say analyzing hundreds of items rapidly), we’ll ensure the cache writes don’t become a bottleneck – for instance, using batch commits or letting writes happen in a background thread. The MultiLayerCache uses write-through for reliability, but we might consider if bulk inserts or periodic flush is needed (likely not for our scale, since decisions happen one per release typically).

Logging and Debug Overhead: We will keep an eye on the overhead introduced by detailed logging. While we want rich logs (see Observability below), we’ll use appropriate log levels (DEBUG for very detailed step-by-step, INFO for high-level decisions) so that in production the performance isn’t hurt by excessive I/O. The metrics collection by CacheMetrics is lightweight (incrementing counters in memory), so that shouldn’t be an issue.

Resource Management: The system will manage resources like disk space for samples or memory usage:

If the analysis involves downloading samples (e.g., first and last pieces of a video to verify completeness), we should ensure these are cleaned up after analysis to not waste disk space.

The cache persistent storage should be monitored (the MultiLayerCache can expire old entries beyond a certain age). We might provide a cleanup call (and indeed decision_cache.cleanup_expired() exists to trim old data).

Memory usage from caching is bounded by an LRU maxsize (1000 by default, configurable), and we can adjust that if needed for more or fewer entries depending on the environment (e.g., an environment with thousands of episodes might increase it).

Overall, these concurrency and performance strategies mean the unified preflight system will be efficient and scalable. It will handle the common case (a few episodes at a time) quickly, and also scale to heavy scenarios (an entire season analysis or many parallel requests) by utilizing caching and parallel processing. Importantly, we do this without sacrificing the determinism of the decisions – thanks to caching and careful design, analyzing many episodes together yields the same decisions as if done one by one, just much faster.

Observability (Logging & Metrics)

For a system responsible for automated decisions, observability is key. We will implement comprehensive logging and metrics so that we can monitor the system’s behavior and troubleshoot issues easily. The observability plan includes:

Detailed Logging:

Every major step in the preflight analysis will emit log messages. This includes:

When preflight_tv_show is invoked, log the series and episodes being processed, and the mode (so we know if it’s standard vs reliability, etc.).

If multiple episodes are requested, log whether a season pack search is being attempted and the result of that search (e.g., “Found season pack XYZ for Show S1” or “No pack found, proceeding with individual episodes”).

For each release analyzed (episode or pack), log the start of analysis (including release name or GUID) and whether we are using a cached result or doing fresh analysis. For example: “Analyzing release GUID 12345 (Episode 5) – cache hit, skipping deep analysis” or conversely “Cache miss for GUID 67890, running full analysis on Episode 6”.

Log outcomes of analysis: the decision (ACCEPT/REJECT and reason). E.g., “Episode 5 accepted (720p WEBRip, no issues found, risk=0.02)” or “Season pack rejected due to missing 2 episodes (incomplete)”. These messages help operators understand why something was or wasn’t downloaded.

If in hybrid mode and any fresh checks are performed despite cache, log those events too (e.g. “Re-checked integrity for Episode 7 – passed”).

Deduplication events: if we skip analyzing an episode because it’s covered by an already analyzed pack, log that (to avoid confusion if someone sees no separate analysis for that episode).

Callback invocations: log when we call the callback to trigger a download, including what is being downloaded (episode or pack). For example: “Triggering download for ‘Show S1 Pack 1080p’ covering E01-E10”. This ties the analysis decision to the action taken.

We will use log levels appropriately. Likely:

INFO level for high-level decisions and actions (pack vs episodes decision, accepted/rejected outcomes).

DEBUG level for finer details (cache hits/misses, internal variable values, time taken for each step, etc.). This way, in normal operation one might run at INFO to see what’s happening broadly, and use DEBUG for deep troubleshooting.

We’ll also ensure logs include identifying information like show name or ID, episode numbers, and release identifiers so they can be correlated. Including the GUID or content key in logs (as the cache logger does) is useful for cross-referencing with cache metrics. For instance, CacheLogger might log “HIT for guid=XYZ content=Show.S01E05”, and we can include similar context in our logs.

Metrics and Telemetry:

Cache Metrics: As discussed, CacheMetrics tracks usage stats. We can periodically extract these (or perhaps at the end of a preflight_tv_show run) to log a summary. For example, after processing a batch of episodes, we could log something like: “Cache stats: 5 lookups, 4 hits, 1 miss (80% hit rate), 1 new entry stored”. Over time, such logs (or if we export metrics to a monitoring system) will tell us how effective our caching is and if our TTLs are appropriate.

Analysis Duration: We will measure how long each analysis takes and potentially output metrics or logs for it:

Start a timer when an analysis begins, stop when it ends. Log the duration: e.g., “Analyzed Episode 5 in 3.2 seconds” or “Pack analysis took 15 seconds for 10 episodes”. If certain episodes consistently take long, this is visible.

We could aggregate timings for a batch: “Total preflight_tv_show execution time: 20 seconds for 5 episodes (including pack analysis)”. This can help identify performance bottlenecks or improvements in the future.

Parallelism Metrics: We might track how many threads/tasks are running or queued. For example, if using asyncio, logging when tasks start/finish gives a sense of concurrency. If using threads, maybe log thread pool usage (though that’s more internal). This isn’t as critical but is useful for debugging if things appear to run sequentially when they shouldn’t.

Quality/Risk Metrics: The analysis results themselves have interesting data – e.g., risk scores, missing ratios. We could gather these to identify trends (like which releases often have high missing_ratio). While this might be beyond the immediate requirement, the data is there. For now, we’ll at least log those values with decisions. If we were to push to a monitoring system, we could imagine capturing, say, a count of “incomplete releases rejected” over time to see how often we avoid bad downloads.

Health Monitoring: The cache system includes a CacheHealthMonitor (attached to DecisionCache) that can provide a health status and performance report. We will utilize that for ongoing observability:

For instance, we could have a periodic job or a debug command to output decision_cache.get_health_status() or get_performance_report(). This might show things like cache size, hit rate, any anomalies (like if reconciliation is happening a lot, etc.). This is useful in development or if an admin suspects the cache is not performing.

If the health monitor finds issues (perhaps it could detect if cache access is slow or error rates), we might surface that in logs or alerts.

Example Log Sequence:
Imagine running preflight_tv_show on a season with episodes 1-3:

Log: “Preflight analysis started for Series=123 (MyShow), Episodes=[S01E01, S01E02, S01E03], Mode=standard.”

Log: “Searching for Season 1 pack… found candidate ‘MyShow.S01.1080p-pack’ on IndexerX.” (or “no pack found”).

Log: “Analyzing season pack release: MyShow.S01.1080p-pack [GUID XYZ]”.

Then either “Cache hit for pack GUID XYZ, decision=ACCEPT (from cache)” or “Cache miss for pack GUID XYZ, performing analysis”.

If analysis done: “Pack analysis complete – decision: ACCEPT, contains 3 episodes, risk=0.01, missing_ratio=0.0.”

Log: “Season pack accepted; will use this for episodes 1-3. Skipping individual episode analysis.” (If pack was good.)

Log: “Invoking download callback for season pack GUID XYZ (covers S01E01-E03).”

Log: “Preflight analysis completed for Series 123, Episodes 1-3 – chosen 1 releases (pack) [Total time=8.5s, Cache hits=1, Misses=1].”
If the pack was rejected:

Log: “Pack was rejected (incomplete), proceeding with individual episode analysis.”

Then logs for each episode: “Analyzing Episode S01E01 release … [GUID]” etc., possibly each with cache status and result.

Finally, decisions and callback for each episode, and a summary log at end.

These logs and metrics will greatly aid in observing system behavior in production. If something goes wrong (say, an episode was not downloaded when it should have been), the logs will show each decision step and whether a cache entry was used or a pack was chosen, etc. Similarly, metrics can indicate if perhaps the cache isn’t being effective (e.g., many misses might show TTLs are too short for a binge-watch scenario). By building observability in from the start, we ensure the system is not a black box – it’s transparent and tunable.

Maintaining Compatibility with Sonarr Workflows

Compatibility with existing systems (especially Sonarr) is a design requirement. We will ensure that our new module plugs into the current workflow without disruption:

Sonarr Integration Points: Sonarr typically automates finding and downloading episodes. Our preflight analysis likely acts as a gatekeeper or decision maker in that flow (perhaps via a custom script or an intermediate service). To remain compatible:

We will not change how Sonarr calls into our system. If Sonarr was configured to call a certain script or API, that remains the same. The difference is internally we route to preflight_tv_show now.

If Sonarr expects certain outputs from our process, we produce them. For example, Sonarr might expect that when a release is rejected by preflight, it gets marked as rejected to avoid reattempt. Our system should ensure that still happens – possibly by returning a status or by invoking a Sonarr API via sonarr_client (e.g., to blacklist a bad release). We’ll mirror whatever the current integrated_selector did in such cases.

Sonarr’s post-processing or notifications: Some setups have Sonarr call a script after download. Our changes are pre-download, so they shouldn’t affect post-download scripts. But it’s worth ensuring that if Sonarr was reading the old DecisionHistory file for info (unlikely, but just in case), the new DecisionCache is writing in a compatible way. In fact, DecisionCache migrated the old JSON and continues to maintain the same API for get/put, so Sonarr or any other component that might read that JSON should still see expected data (and we keep the alias DecisionHistory for code references).

Return Format and Data Contracts: The output of preflight_tv_show will match what the system currently expects from TV analysis:

If previously the integrated selector function returned a Python dict or a JSON string with decisions, we do the same. For instance, it might return something like:

{
  "episode_12345": {"decision": "accept", "release_title": "...", "quality": "...", "download": true},
  "episode_12346": {"decision": "reject", "reason": "incomplete file"}
}


(Exact format is hypothetical.) We will ensure our result adheres to this structure so that any consumer (maybe a UI or Sonarr) can parse it as before. If the integrated_selector didn’t return anything but instead directly initiated downloads and logged to a file, we might keep that behavior (i.e., preflight_tv_show could return None and just perform actions). In that case, compatibility means replicating side effects (like logging to a known location or updating a status somewhere).

The DecisionCache ensures that if Sonarr or other processes look at the old decision_history.json, it stays updated. The code snippet showed they migrate the legacy data and likely keep writing to JSON as needed for compatibility. We should confirm if the new system still writes a human-readable cache file or only SQLite. If only SQLite, and something external needed the JSON, we might implement an export for compatibility. (However, since DecisionHistory is basically replaced entirely, probably no external direct usage of that file is needed).

Download Execution: Sonarr expects downloads to be initiated in a certain way (usually via its internal download client integration or a release push). With our callback approach, we ensure that the actual download trigger uses Sonarr’s normal path:

One way is using Sonarr’s API: e.g., calling the /api/release endpoint to tell Sonarr “grab this release”. If sonarr_client has a method for that, our callback will use it. This way, Sonarr handles sending to the downloader and tracking it in its queue, just as if it had grabbed it itself.

Alternatively, if the current system passed the torrent/NZB directly to a downloader (bypassing Sonarr), we can continue to do that. But keeping Sonarr in the loop is usually better for tracking. We’ll follow the existing integration method to not surprise Sonarr.

Importantly, only approved releases should be downloaded. Any that are rejected by preflight must not be sent to download. Sonarr should mark them as rejected or simply not try again from that indexer. Possibly Sonarr will attempt another release. Our system might communicate the rejection by exiting with a certain code or calling a Sonarr API to reject the release. We’ll maintain that mechanism.

Maintaining Sonarr’s Workflow Timing: Sonarr typically triggers searches and then expects a quick response on what to grab. Our unified module should be as fast or faster than the old logic, which the caching and concurrency help with. As long as we don’t introduce excessive delays, Sonarr will operate normally (if we were too slow, Sonarr might time out or move on – but with caching, subsequent calls are faster than ever). We will test scenarios like a search for 10 episodes to ensure it completes in a reasonable time (with cached results, likely only seconds).

Testing with Sonarr: Before full deployment, we would run the new module in a staging setup with Sonarr:

Add a show, let Sonarr trigger a search for missing episodes, and see if our module correctly picks a release and initiates download.

Try both individual episode grabs and season pack grabs to ensure both pathways work and Sonarr handles them (Sonarr can accept season pack downloads if the download client labels them properly, etc.).

Check that Sonarr’s UI reflects what happened (e.g., episodes marked as grabbing/downloading after our module runs).

Confirm that if our module rejects all releases (like if everything was bad quality), Sonarr doesn’t download anything and perhaps tries again later – essentially that we don’t break Sonarr’s retry logic.

In essence, from Sonarr’s perspective, nothing “new” should be noticed – except perhaps more intelligent decision-making. The integration remains seamless: Sonarr triggers a search, our preflight selector runs (via whatever hook, perhaps a custom script), and then downloads happen (or not) just as they should, with Sonarr continuing to manage the media library.

Conclusion

This architecture proposal outlines a robust, unified approach for TV show preflight analysis that meets all the specified requirements. By consolidating logic into tv_show_preflight_selector.py and leveraging advanced caching and parallelism, we achieve:

Architectural Clarity: A single, well-organized module for TV preflight logic, analogous to the movie preflight module, making the system easier to understand and maintain. No more fragmented or duplicated code for TV logic – everything funnels through one clear entry point and flow.

Flexible Analysis Modes: The ability to toggle between standard, hybrid, and reliability modes gives operators control over caching vs thoroughness, without changing code. The design cleanly incorporates these modes, reusing cached decisions when desired and doing fresh analysis when required.

Intelligent TV Workflow Handling: Complex scenarios like multi-episode files and season packs are first-class considerations in this design. The system can decide the optimal download strategy (pack vs episodes) and handle special cases like two-part episodes gracefully. This ensures that it matches the functionality of the old system and improves upon it by structuring the logic more coherently.

Performance through Caching and Concurrency: The use of the new multi-layer DecisionCache provides better performance and GUID reconciliation, avoiding redundant work. Combined with parallel processing of tasks, this means faster end-to-end processing, whether it’s one episode or an entire season. Users will benefit from quicker response times and the ability to scale to large batch operations.

Rich Observability: We have built in extensive logging and metrics collection, from cache metrics (hits/misses) to analysis timings and decision logs. This makes the system transparent; developers and admins can monitor it in real-time and gain insights (for example, seeing a log of cache hits confirms the cache is working, seeing metrics on incomplete files helps gauge source reliability, etc.). Troubleshooting and optimizing will be much easier with this visibility.

Seamless Integration & Compatibility: The new module is designed to slot into the existing Sonarr-driven workflow without disruption. All external interfaces (function calls, return formats, cache files) remain compatible. Thus, we can deploy the new system and retire the old integrated_selector with minimal risk – Sonarr and other components should continue to work as before, now benefiting from the improved internals.

In conclusion, this unified cache-aware preflight analysis system will provide a cleaner, faster, and more maintainable solution for TV show download decision-making. It takes the proven patterns from movie analysis and adapts them to the nuances of TV, all while ensuring that performance and observability are top-notch. By following this design and implementation strategy, we will achieve a robust system ready to support current needs and easy to extend for future enhancements. The detailed plan above can now be used as a blueprint for development, guiding the coding, testing, and integration phases to ensure all requirements are met.

Sources:

DecisionCache and multi-layer cache design in the existing system (demonstrating use of multi-layer architecture with observability).

Excerpts from the caching module showing performance and GUID reconciliation focus.

Sonarr forum discussion confirming multi-episode release naming conventions
forums.sonarr.tv
forums.sonarr.tv
 (useful for our multi-episode detection logic).

Multi-layer cache stats reporting (for monitoring cache effectiveness).