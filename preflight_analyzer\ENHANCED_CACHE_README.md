# Enhanced Multi-Layer Media Cache Architecture

This document describes the upgraded multi-layer cache architecture that provides high hit rates, reliability, and advanced features for the PlexAutomator preflight analyzer.

## Overview

The enhanced cache system implements a three-layer architecture with smart coordination:

- **L1 (Memory Cache)**: Fast in-memory LRU cache for hot data with sub-millisecond latency
- **L2 (Persistent Cache)**: Durable SQLite-based cache for warm data with long-term retention
- **L3 (GUID Reconciler)**: Content-based similarity matcher for deduplication across releases

## Key Features

### 1. Selective Deduplication Modes

The cache supports three analysis modes to control deduplication behavior:

#### Standard Mode (`"standard"`)
- **Purpose**: Maximize deduplication for normal operation
- **Behavior**: Reuses cached decisions whenever possible through GUID reconciliation
- **Use Case**: Default mode for typical usage, prioritizes efficiency

#### Reliability Mode (`"reliability"`)
- **Purpose**: Force fresh analysis for research and reliability studies
- **Behavior**: Bypasses deduplication, each GUID gets independent analysis
- **Use Case**: Gathering per-release data to evaluate indexer/group reliability

#### Hybrid Mode (`"hybrid"`)
- **Purpose**: Balance between deduplication and fresh reliability checks
- **Behavior**: Reuses metadata but performs fresh reliability checks
- **Use Case**: Maintaining efficiency while still gathering reliability metrics

### 2. Proactive Cache Warming

The cache warming system pre-populates likely-needed analyses:

- **Upcoming Releases**: Anticipates content based on schedules and patterns
- **User Activity**: Analyzes recent searches and download patterns
- **Popular Content**: Pre-analyzes trending and popular releases
- **Background Processing**: Runs during off-peak hours to avoid interference

### 3. TTL Coordination with Staggered Expiration

Prevents cache avalanches through intelligent expiration:

- **Synchronized Expiration**: L1 and L2 layers coordinate TTL policies
- **Staggered TTL**: Random jitter prevents simultaneous expiration of many entries
- **Policy-Based**: Different TTL policies for different content types and reliability levels
- **Proactive Refresh**: Refreshes frequently accessed content before expiration

### 4. Enhanced GUID Reconciliation

Improved matching logic with configurable sensitivity:

- **Confidence Thresholds**: Adjustable per analysis mode
- **Size/Quality Heuristics**: Better matching based on file characteristics
- **Group/Indexer Sensitivity**: Configurable cross-group and cross-indexer matching
- **Weighted Scoring**: Configurable weights for title, size, and quality matching

### 5. Comprehensive Observability

Enhanced metrics and monitoring:

- **Analysis Mode Tracking**: Usage statistics per mode
- **Indexer/Group Reliability**: Success/failure rates per source
- **Cache Warming Effectiveness**: Metrics on warming task success
- **Deduplication Statistics**: Confidence scores and match rates
- **Performance Monitoring**: Latency tracking and health alerts

## Usage Examples

### Basic Usage with Analysis Modes

```python
from preflight_analyzer import MultiLayerCache, AnalysisMode

# Initialize cache
cache = MultiLayerCache(cache_dir="./cache")

# Standard mode (default) - maximize deduplication
result = cache.get_analysis(
    guid="release_guid_123",
    title="Movie Title 2023 1080p BluRay x264-GROUP",
    size_bytes=**********,
    indexer="indexer1",
    analysis_mode=AnalysisMode.STANDARD
)

# Reliability mode - force fresh analysis
result = cache.get_analysis(
    guid="release_guid_456", 
    title="Movie Title 2023 1080p BluRay x264-OTHERGROUP",
    size_bytes=**********,
    indexer="indexer2",
    analysis_mode=AnalysisMode.RELIABILITY
)
```

### Cache Warming

```python
from preflight_analyzer import CacheWarmer, ContentKey

# Initialize warmer
warmer = CacheWarmer(cache=cache)

# Add warming hints
content_key = ContentKey(
    content_type="movie",
    title="Popular Movie 2024",
    year=2024,
    quality="1080p"
)

warmer.add_warming_hint(
    content_key=content_key,
    priority=80,
    source="user_activity"
)
```

### Enhanced Observability

```python
from preflight_analyzer import CacheMetrics

# Initialize metrics
metrics = CacheMetrics()

# Record events
metrics.record_analysis_mode_usage("standard", "hit")
metrics.record_indexer_result("indexer1", success=True)
metrics.record_warming_task("completed", time_saved_ms=1500.0)

# Get comprehensive stats
stats = cache.get_cache_stats()
print(f"Cache hit rate: {stats['requests']['hit_rate']:.1%}")
print(f"Indexer reliability: {stats['indexer_reliability']}")
```

## Configuration

### TTL Policies

```python
from preflight_analyzer import TTLCoordinator, TTLPolicy, TTLConfig

coordinator = TTLCoordinator()

# Customize TTL for short-lived data
coordinator.update_policy(
    TTLPolicy.SHORT,
    TTLConfig(
        base_ttl_seconds=1800,  # 30 minutes
        jitter_percent=15.0,
        min_ttl_seconds=300,
        max_ttl_seconds=3600
    )
)
```

### GUID Reconciler Settings

```python
from preflight_analyzer import GuidReconciler

reconciler = GuidReconciler(
    size_tolerance=0.05,  # 5% size difference tolerance
    min_confidence_score=0.7,
    deduplicate_across_groups=True,
    deduplicate_across_indexers=True,
    quality_weight=0.3,
    size_weight=0.4,
    title_weight=0.3
)
```

## Performance Benefits

The enhanced architecture targets:

- **~97% cache hit rate** through intelligent deduplication and warming
- **Sub-millisecond L1 response times** for hot data
- **Reduced analysis workload** through proactive warming
- **Improved reliability insights** through granular per-release tracking
- **Stable performance** through staggered expiration and load balancing

## Migration and Compatibility

The enhanced cache maintains backward compatibility:

- **Legacy API Support**: Existing `DecisionHistory` interface continues to work
- **Automatic Migration**: Legacy cache data is imported on first run
- **Gradual Adoption**: New features are opt-in with sensible defaults
- **Zero Downtime**: Upgrades don't require cache rebuilding

## Monitoring and Maintenance

### Health Monitoring

```python
# Check cache health
health = cache.get_health_status()
if health['status'] != 'healthy':
    print(f"Cache issues detected: {health['alerts']}")

# Get performance report
report = cache.get_performance_report()
print(report)
```

### Maintenance Tasks

```python
# Clean up expired entries
deleted_count = cache.cleanup_expired()
print(f"Cleaned up {deleted_count} expired entries")

# Get warming statistics
warming_stats = warmer.get_warming_stats()
print(f"Warming effectiveness: {warming_stats.cache_hits_generated} hits generated")
```

## Architecture Benefits

1. **High Hit Rates**: Multi-layer approach with intelligent reconciliation
2. **Reliability Research**: Granular per-release analysis in reliability mode
3. **Performance**: Proactive warming eliminates cold-start penalties
4. **Stability**: Staggered expiration prevents cache avalanches
5. **Observability**: Comprehensive metrics for optimization and debugging
6. **Flexibility**: Configurable behavior for different use cases
7. **Scalability**: Efficient storage and retrieval with SQLite backend

This enhanced architecture provides a robust foundation for high-performance media analysis with comprehensive reliability tracking capabilities.
