"""
GUID Evolution and Reconciliation System.

This module handles the reconciliation of new GUIDs with existing cache entries
using content-based heuristics. It implements the logic to match releases that
are essentially the same content but have different GUIDs due to indexer changes.
"""

from __future__ import annotations
import logging
import re
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass

from .cache_models import AnalysisResult, ContentKey, AnalysisMode, extract_quality_from_title

logger = logging.getLogger(__name__)


@dataclass
class MatchCandidate:
    """A potential match for GUID reconciliation."""
    analysis_result: AnalysisResult
    confidence_score: float
    match_reasons: List[str]
    
    def __lt__(self, other):
        return self.confidence_score < other.confidence_score


class GuidReconciler:
    """Handles GUID evolution and content matching heuristics."""
    
    def __init__(self,
                 size_tolerance: float = 0.05,  # 5% size difference tolerance
                 title_similarity_threshold: float = 0.8,
                 min_confidence_score: float = 0.7,
                 deduplicate_across_groups: bool = True,
                 deduplicate_across_indexers: bool = True,
                 quality_weight: float = 0.3,
                 size_weight: float = 0.4,
                 title_weight: float = 0.3):
        """
        Initialize GUID reconciler.

        Args:
            size_tolerance: Maximum relative size difference to consider a match
            title_similarity_threshold: Minimum title similarity for matching
            min_confidence_score: Minimum confidence score to accept a match
            deduplicate_across_groups: Whether to match releases from different groups
            deduplicate_across_indexers: Whether to match releases from different indexers
            quality_weight: Weight for quality matching in confidence score
            size_weight: Weight for size matching in confidence score
            title_weight: Weight for title matching in confidence score
        """
        self.size_tolerance = size_tolerance
        self.title_similarity_threshold = title_similarity_threshold
        self.min_confidence_score = min_confidence_score
        self.deduplicate_across_groups = deduplicate_across_groups
        self.deduplicate_across_indexers = deduplicate_across_indexers

        # Confidence score weights (should sum to 1.0)
        total_weight = quality_weight + size_weight + title_weight
        self.quality_weight = quality_weight / total_weight
        self.size_weight = size_weight / total_weight
        self.title_weight = title_weight / total_weight

        logger.info(f"Initialized GUID reconciler with size_tolerance={size_tolerance}, "
                   f"title_threshold={title_similarity_threshold}, min_confidence={min_confidence_score}, "
                   f"groups={deduplicate_across_groups}, indexers={deduplicate_across_indexers}")

    def get_confidence_threshold(self, analysis_mode: AnalysisMode) -> float:
        """
        Get confidence threshold adjusted for analysis mode.

        Args:
            analysis_mode: The analysis mode being used

        Returns:
            Adjusted confidence threshold
        """
        base_threshold = self.min_confidence_score

        if analysis_mode == AnalysisMode.STANDARD:
            # Standard mode: use lower threshold to maximize reuse
            return base_threshold * 0.9
        elif analysis_mode == AnalysisMode.RELIABILITY:
            # Reliability mode: should not be used (bypassed), but if called, use high threshold
            return base_threshold * 1.5
        elif analysis_mode == AnalysisMode.HYBRID:
            # Hybrid mode: use higher threshold to be more cautious
            return base_threshold * 1.1
        else:
            return base_threshold

    def find_matching_content(self,
                            new_guid: str,
                            new_title: str,
                            new_size: int,
                            new_indexer: str,
                            content_type: str,
                            existing_results: List[AnalysisResult],
                            year: Optional[int] = None,
                            season: Optional[int] = None,
                            episode: Optional[int] = None,
                            analysis_mode: AnalysisMode = AnalysisMode.STANDARD) -> Optional[MatchCandidate]:
        """
        Find the best matching existing content for a new GUID.
        
        Args:
            new_guid: The new GUID to match
            new_title: Release title
            new_size: File size in bytes
            new_indexer: Indexer name
            content_type: 'movie' or 'tv'
            existing_results: List of existing analysis results to match against
            year: Movie year (for movies)
            season: Season number (for TV)
            episode: Episode number (for TV)
            analysis_mode: Analysis mode affecting confidence thresholds

        Returns:
            Best matching candidate or None if no good match found
        """
        if not existing_results:
            return None
        
        candidates = []
        confidence_threshold = self.get_confidence_threshold(analysis_mode)

        for existing in existing_results:
            confidence_score, match_reasons = self._calculate_match_confidence(
                new_title, new_size, new_indexer, content_type,
                existing, year, season, episode
            )

            if confidence_score >= confidence_threshold:
                candidates.append(MatchCandidate(
                    analysis_result=existing,
                    confidence_score=confidence_score,
                    match_reasons=match_reasons
                ))
        
        if not candidates:
            logger.debug(f"No matching candidates found for GUID {new_guid}")
            return None
        
        # Return the best match
        best_match = max(candidates)
        logger.info(f"Found match for GUID {new_guid}: {best_match.analysis_result.content_key.to_string()} "
                   f"(confidence: {best_match.confidence_score:.3f}, reasons: {', '.join(best_match.match_reasons)})")
        
        return best_match
    
    def _calculate_match_confidence(self,
                                  new_title: str,
                                  new_size: int,
                                  new_indexer: str,
                                  content_type: str,
                                  existing: AnalysisResult,
                                  year: Optional[int] = None,
                                  season: Optional[int] = None,
                                  episode: Optional[int] = None) -> Tuple[float, List[str]]:
        """Calculate confidence score for a potential match."""
        confidence = 0.0
        reasons = []
        
        # Content type must match
        if existing.content_key.content_type != content_type:
            return 0.0, ["content_type_mismatch"]
        
        # Title similarity (weighted)
        title_sim = self._calculate_title_similarity(new_title, existing.title)
        if title_sim >= self.title_similarity_threshold:
            confidence += self.title_weight * title_sim
            reasons.append(f"title_similarity_{title_sim:.2f}")
        else:
            # If title similarity is too low, this is probably not a match
            return 0.0, ["title_similarity_too_low"]

        # Size similarity (weighted)
        size_sim = self._calculate_size_similarity(new_size, existing.size_bytes)
        if size_sim >= (1.0 - self.size_tolerance):
            confidence += self.size_weight * size_sim
            reasons.append(f"size_similarity_{size_sim:.3f}")

        # Quality similarity (weighted)
        new_quality = extract_quality_from_title(new_title)
        existing_quality = existing.content_key.quality
        quality_sim = self._calculate_quality_similarity(new_quality, existing_quality)
        confidence += self.quality_weight * quality_sim
        if quality_sim > 0.8:
            reasons.append(f"quality_similarity_{quality_sim:.2f}")

        # Group sensitivity check
        if not self.deduplicate_across_groups:
            new_group = self._extract_group_from_title(new_title)
            existing_group = self._extract_group_from_title(existing.title)
            if new_group and existing_group and new_group != existing_group:
                return 0.0, ["different_groups_not_allowed"]

        # Indexer sensitivity check
        if not self.deduplicate_across_indexers:
            if new_indexer != existing.indexer:
                return 0.0, ["different_indexers_not_allowed"]
        
        # Content-specific matching
        if content_type == 'movie':
            # Year matching for movies
            if year and existing.content_key.year:
                if year == existing.content_key.year:
                    confidence += 0.15
                    reasons.append("year_exact_match")
                elif abs(year - existing.content_key.year) <= 1:
                    confidence += 0.05
                    reasons.append("year_close_match")
        
        elif content_type == 'tv':
            # Season/episode matching for TV
            if season is not None and existing.content_key.season is not None:
                if season == existing.content_key.season:
                    confidence += 0.1
                    reasons.append("season_match")
                    
                    if episode is not None and existing.content_key.episode is not None:
                        if episode == existing.content_key.episode:
                            confidence += 0.1
                            reasons.append("episode_match")
        
        # Quality similarity
        new_quality = extract_quality_from_title(new_title)
        if new_quality and existing.content_key.quality:
            quality_sim = self._calculate_quality_similarity(new_quality, existing.content_key.quality)
            confidence += 0.05 * quality_sim
            if quality_sim > 0.5:
                reasons.append(f"quality_similarity_{quality_sim:.2f}")
        
        # Same indexer bonus (same content often reappears on same indexer)
        if new_indexer == existing.indexer:
            confidence += 0.05
            reasons.append("same_indexer")
        
        return min(confidence, 1.0), reasons
    
    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """Calculate similarity between two titles."""
        # Normalize titles
        norm1 = self._normalize_title_for_comparison(title1)
        norm2 = self._normalize_title_for_comparison(title2)
        
        if norm1 == norm2:
            return 1.0
        
        # Use Jaccard similarity on word sets
        words1 = set(norm1.split())
        words2 = set(norm2.split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _normalize_title_for_comparison(self, title: str) -> str:
        """Normalize title for comparison."""
        # Convert to lowercase
        title = title.lower()
        
        # Remove common release group patterns
        title = re.sub(r'-[a-z0-9]+$', '', title)  # Remove trailing release group
        
        # Remove quality indicators
        quality_patterns = [
            r'\b\d+p\b', r'\b4k\b', r'\buhd\b', r'\bweb-?dl\b', r'\bbluray\b', r'\bblu-ray\b',
            r'\bhdtv\b', r'\bsdtv\b', r'\bx264\b', r'\bx265\b', r'\bhevc\b', r'\bh\.264\b', r'\bh\.265\b',
            r'\baac\b', r'\bac3\b', r'\bdts\b', r'\batmos\b'
        ]
        for pattern in quality_patterns:
            title = re.sub(pattern, '', title, flags=re.IGNORECASE)
        
        # Remove year in parentheses or brackets
        title = re.sub(r'[\(\[]?\b(19|20)\d{2}\b[\)\]]?', '', title)
        
        # Remove dots and underscores, normalize spaces
        title = re.sub(r'[._]', ' ', title)
        title = re.sub(r'\s+', ' ', title).strip()
        
        return title
    
    def _calculate_size_similarity(self, size1: int, size2: int) -> float:
        """Calculate similarity between two file sizes."""
        if size1 == size2:
            return 1.0
        
        if size1 == 0 or size2 == 0:
            return 0.0
        
        # Calculate relative difference
        larger = max(size1, size2)
        smaller = min(size1, size2)
        
        relative_diff = (larger - smaller) / larger
        return max(0.0, 1.0 - relative_diff)
    
    def _calculate_quality_similarity(self, quality1: Optional[str], quality2: Optional[str]) -> float:
        """Calculate similarity between quality strings."""
        if not quality1 or not quality2:
            return 0.5  # Neutral score if quality unknown

        quality1 = quality1.lower()
        quality2 = quality2.lower()

        if quality1 == quality2:
            return 1.0

        # Define quality tiers for similarity
        quality_tiers = {
            'cam': 1, 'ts': 1, 'tc': 1,
            'dvdscr': 2, 'r5': 2, 'r6': 2,
            'dvdrip': 3, 'bdrip': 3, 'brrip': 3,
            '720p': 4, 'hdtv': 4,
            '1080p': 5, 'bluray': 5,
            '4k': 6, '2160p': 6, 'uhd': 6
        }

        tier1 = quality_tiers.get(quality1, 3)  # Default to mid-tier
        tier2 = quality_tiers.get(quality2, 3)

        # Similar tiers get higher similarity
        tier_diff = abs(tier1 - tier2)
        if tier_diff == 0:
            return 1.0
        elif tier_diff == 1:
            return 0.7
        elif tier_diff == 2:
            return 0.4
        else:
            return 0.1
    
    def extract_content_metadata(self, title: str, guid: str, indexer: str) -> Dict[str, Any]:
        """Extract content metadata from release title for matching."""
        metadata = {
            'title': title,
            'guid': guid,
            'indexer': indexer,
            'quality': extract_quality_from_title(title),
            'year': None,
            'season': None,
            'episode': None,
            'content_type': 'movie'  # Default
        }
        
        # Extract year
        year_match = re.search(r'\b(19|20)(\d{2})\b', title)
        if year_match:
            metadata['year'] = int(year_match.group(0))
        
        # Check for TV patterns
        tv_patterns = [
            r'[Ss](\d{1,2})[Ee](\d{1,2})',  # S01E05
            r'(\d{1,2})x(\d{1,2})',         # 1x05
            r'Season\s*(\d+).*Episode\s*(\d+)',  # Season 1 Episode 5
        ]
        
        for pattern in tv_patterns:
            match = re.search(pattern, title, re.IGNORECASE)
            if match:
                metadata['content_type'] = 'tv'
                metadata['season'] = int(match.group(1))
                metadata['episode'] = int(match.group(2))
                break
        
        # Extract series title for TV shows
        if metadata['content_type'] == 'tv':
            # Remove season/episode info and quality to get series title
            clean_title = title
            for pattern in tv_patterns:
                clean_title = re.sub(pattern, '', clean_title, flags=re.IGNORECASE)
            
            # Remove quality and other tags
            clean_title = re.sub(r'\b\d+p\b', '', clean_title, flags=re.IGNORECASE)
            clean_title = re.sub(r'\b(web-?dl|bluray|hdtv|x264|x265)\b', '', clean_title, flags=re.IGNORECASE)
            clean_title = re.sub(r'[._-]', ' ', clean_title)
            clean_title = re.sub(r'\s+', ' ', clean_title).strip()
            
            metadata['series_title'] = clean_title
        
        return metadata

    def _extract_group_from_title(self, title: str) -> Optional[str]:
        """Extract release group from title."""
        import re

        # Common patterns for release groups
        patterns = [
            r'-([A-Za-z0-9]+)$',  # Group at end after dash
            r'\[([A-Za-z0-9]+)\]',  # Group in brackets
            r'\{([A-Za-z0-9]+)\}',  # Group in braces
        ]

        for pattern in patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1).upper()

        return None
