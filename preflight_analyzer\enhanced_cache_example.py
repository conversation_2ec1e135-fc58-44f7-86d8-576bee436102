"""
Enhanced Cache Usage Example

This example demonstrates how to use the upgraded multi-layer cache architecture
with analysis modes, cache warming, and enhanced observability.
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, Any

from .cache_models import AnalysisMode, ContentKey
from .multi_layer_cache import Multi<PERSON>ayer<PERSON>ache
from .cache_warming import <PERSON>ache<PERSON><PERSON><PERSON>
from .cache_observability import CacheMetrics, CacheLogger

logger = logging.getLogger(__name__)


class EnhancedCacheDemo:
    """Demonstrates the enhanced cache features."""
    
    def __init__(self, cache_dir: Path):
        """Initialize the enhanced cache demo."""
        self.cache_dir = cache_dir
        
        # Initialize multi-layer cache
        self.cache = MultiLayerCache(
            cache_dir=cache_dir,
            memory_maxsize=1000,
            memory_ttl_seconds=3600,
            persistent_max_age_days=365
        )
        
        # Initialize cache warming
        self.warmer = CacheWarmer(
            cache=self.cache,
            max_concurrent_tasks=3,
            max_queue_size=100,
            warming_interval_hours=6
        )
        
        # Initialize observability
        self.metrics = CacheMetrics()
        self.cache_logger = CacheLogger(self.metrics)
        
        logger.info("Enhanced cache demo initialized")
    
    def demonstrate_analysis_modes(self):
        """Demonstrate different analysis modes."""
        print("\n=== Analysis Mode Demonstration ===")
        
        # Sample release data
        releases = [
            {
                "guid": "movie_1080p_group1",
                "title": "Example Movie 2023 1080p BluRay x264-GROUP1",
                "size_bytes": 8_000_000_000,
                "indexer": "indexer1"
            },
            {
                "guid": "movie_1080p_group2", 
                "title": "Example Movie 2023 1080p BluRay x264-GROUP2",
                "size_bytes": 8_100_000_000,
                "indexer": "indexer2"
            }
        ]
        
        # Test different analysis modes
        modes = [AnalysisMode.STANDARD, AnalysisMode.RELIABILITY, AnalysisMode.HYBRID]
        
        for mode in modes:
            print(f"\n--- Testing {mode.value.upper()} mode ---")
            
            for release in releases:
                result = self.cache.get_analysis(
                    guid=release["guid"],
                    title=release["title"],
                    size_bytes=release["size_bytes"],
                    indexer=release["indexer"],
                    content_type="movie",
                    year=2023,
                    analysis_mode=mode
                )
                
                if result:
                    print(f"✓ Cache hit for {release['guid']} in {mode.value} mode")
                else:
                    print(f"✗ Cache miss for {release['guid']} in {mode.value} mode")
                    
                    # In a real scenario, you would perform analysis here
                    # and then store the result
                    print(f"  → Would perform fresh analysis for {release['guid']}")
    
    def demonstrate_cache_warming(self):
        """Demonstrate proactive cache warming."""
        print("\n=== Cache Warming Demonstration ===")
        
        # Add some warming hints
        content_keys = [
            ContentKey(
                content_type="movie",
                title="Popular Movie 2024",
                year=2024,
                quality="1080p"
            ),
            ContentKey(
                content_type="tv",
                title="Popular Series",
                season=1,
                episode=5,
                quality="720p"
            )
        ]
        
        for content_key in content_keys:
            self.warmer.add_warming_hint(
                content_key=content_key,
                priority=80,
                source="demo",
                metadata={"reason": "anticipated_popular_content"}
            )
            print(f"✓ Added warming hint for: {content_key.to_string()}")
        
        # Show warming queue status
        status = self.warmer.get_queue_status()
        print(f"\nWarming queue status:")
        print(f"  Queue size: {status['queue_size']}")
        print(f"  Active tasks: {status['active_tasks']}")
        print(f"  Max concurrent: {status['max_concurrent']}")
    
    def demonstrate_observability(self):
        """Demonstrate enhanced observability features."""
        print("\n=== Observability Demonstration ===")
        
        # Record some sample metrics
        self.metrics.record_analysis_mode_usage("standard", "hit")
        self.metrics.record_analysis_mode_usage("reliability", "miss")
        self.metrics.record_indexer_result("indexer1", True)
        self.metrics.record_indexer_result("indexer2", False)
        self.metrics.record_group_result("GROUP1", True)
        self.metrics.record_warming_task("completed", 1500.0)
        self.metrics.record_reconciliation_attempt(True, 0.85)
        
        # Get comprehensive stats
        stats = self.cache.get_cache_stats()
        
        print("Cache Statistics:")
        print(f"  L1 hits: {stats.get('l1_hits', 0)}")
        print(f"  L2 hits: {stats.get('l2_hits', 0)}")
        print(f"  Total misses: {stats.get('total_misses', 0)}")
        print(f"  GUID reconciliations: {stats.get('guid_reconciliations', 0)}")
        
        # Show analysis mode usage
        mode_stats = stats.get('analysis_modes', {})
        if mode_stats:
            print("\nAnalysis Mode Usage:")
            for mode, events in mode_stats.items():
                print(f"  {mode}: {dict(events)}")
        
        # Show indexer reliability
        indexer_reliability = stats.get('indexer_reliability', {})
        if indexer_reliability:
            print("\nIndexer Reliability:")
            for indexer, metrics in indexer_reliability.items():
                success_rate = metrics.get('success_rate', 0) * 100
                print(f"  {indexer}: {success_rate:.1f}% success rate")
    
    def demonstrate_guid_reconciliation(self):
        """Demonstrate enhanced GUID reconciliation."""
        print("\n=== GUID Reconciliation Demonstration ===")
        
        # Configure reconciler with different sensitivity settings
        reconciler_configs = [
            {"deduplicate_across_groups": True, "deduplicate_across_indexers": True},
            {"deduplicate_across_groups": False, "deduplicate_across_indexers": True},
            {"deduplicate_across_groups": True, "deduplicate_across_indexers": False}
        ]
        
        for i, config in enumerate(reconciler_configs):
            print(f"\n--- Configuration {i+1}: {config} ---")
            
            # In a real implementation, you would create a new reconciler
            # with these settings and test matching behavior
            print(f"  Groups: {'✓' if config['deduplicate_across_groups'] else '✗'}")
            print(f"  Indexers: {'✓' if config['deduplicate_across_indexers'] else '✗'}")
    
    def run_demo(self):
        """Run the complete demonstration."""
        print("🚀 Enhanced Multi-Layer Cache Architecture Demo")
        print("=" * 50)
        
        try:
            self.demonstrate_analysis_modes()
            self.demonstrate_cache_warming()
            self.demonstrate_observability()
            self.demonstrate_guid_reconciliation()
            
            print("\n" + "=" * 50)
            print("✅ Demo completed successfully!")
            
        except Exception as e:
            print(f"\n❌ Demo failed with error: {e}")
            logger.error(f"Demo failed: {e}", exc_info=True)
        
        finally:
            # Cleanup
            self.warmer.stop_background_warming()
            print("🧹 Cleanup completed")


def main():
    """Main entry point for the demo."""
    logging.basicConfig(level=logging.INFO)
    
    cache_dir = Path("demo_cache")
    demo = EnhancedCacheDemo(cache_dir)
    demo.run_demo()


if __name__ == "__main__":
    main()
