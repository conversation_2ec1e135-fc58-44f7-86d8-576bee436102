"""
TTL Coordination Module - Manages synchronized expiration across cache layers.

This module implements TTL-aware coordination for cache entries to maintain
consistency and prevent stale data while avoiding cache avalanches through
staggered expiration.

Key features:
1. Synchronized expiration across L1 and L2 layers
2. Staggered expiration with random jitter to prevent cache avalanches
3. Configurable TTL policies per content type
4. Proactive refresh for frequently accessed content
"""

from __future__ import annotations
import logging
import random
import time
from dataclasses import dataclass
from typing import Any, Dict, Optional, Tuple
from enum import Enum

logger = logging.getLogger(__name__)


class TTLPolicy(Enum):
    """TTL policy types for different content categories."""
    SHORT = "short"      # Hours (hot data)
    MEDIUM = "medium"    # Days (warm data)
    LONG = "long"        # Weeks/months (cold data)
    PERMANENT = "permanent"  # No expiration


@dataclass
class TTLConfig:
    """Configuration for TTL policies."""
    base_ttl_seconds: int
    jitter_percent: float = 10.0  # ±10% jitter by default
    min_ttl_seconds: int = 300    # Minimum 5 minutes
    max_ttl_seconds: int = 86400 * 365  # Maximum 1 year
    refresh_threshold: float = 0.8  # Refresh when 80% of TTL elapsed


class TTLCoordinator:
    """
    Coordinates TTL policies across cache layers to prevent stale data
    and cache avalanches.
    """
    
    def __init__(self):
        """Initialize TTL coordinator with default policies."""
        self.policies = {
            TTLPolicy.SHORT: TTLConfig(
                base_ttl_seconds=3600,  # 1 hour
                jitter_percent=15.0,
                min_ttl_seconds=300,    # 5 minutes
                max_ttl_seconds=7200    # 2 hours
            ),
            TTLPolicy.MEDIUM: TTLConfig(
                base_ttl_seconds=86400,  # 1 day
                jitter_percent=10.0,
                min_ttl_seconds=3600,    # 1 hour
                max_ttl_seconds=259200   # 3 days
            ),
            TTLPolicy.LONG: TTLConfig(
                base_ttl_seconds=2592000,  # 30 days
                jitter_percent=5.0,
                min_ttl_seconds=86400,     # 1 day
                max_ttl_seconds=7776000    # 90 days
            ),
            TTLPolicy.PERMANENT: TTLConfig(
                base_ttl_seconds=86400 * 365,  # 1 year (effectively permanent)
                jitter_percent=1.0,
                min_ttl_seconds=86400 * 30,    # 30 days minimum
                max_ttl_seconds=86400 * 365 * 2  # 2 years maximum
            )
        }
        
        logger.info("Initialized TTL coordinator with default policies")
    
    def calculate_ttl(self, 
                     policy: TTLPolicy, 
                     content_type: str = "unknown",
                     access_frequency: float = 0.0) -> Tuple[int, float]:
        """
        Calculate TTL with staggered expiration.
        
        Args:
            policy: TTL policy to use
            content_type: Type of content (for policy refinement)
            access_frequency: How frequently this content is accessed (0.0-1.0)
            
        Returns:
            Tuple of (ttl_seconds, expiration_timestamp)
        """
        config = self.policies[policy]
        
        # Start with base TTL
        base_ttl = config.base_ttl_seconds
        
        # Adjust based on access frequency (more frequent = longer TTL)
        if access_frequency > 0.5:
            base_ttl = int(base_ttl * (1.0 + access_frequency * 0.5))
        
        # Apply jitter to prevent cache avalanches
        jitter_range = base_ttl * (config.jitter_percent / 100.0)
        jitter = random.uniform(-jitter_range, jitter_range)
        
        # Calculate final TTL with bounds checking
        final_ttl = int(base_ttl + jitter)
        final_ttl = max(config.min_ttl_seconds, min(final_ttl, config.max_ttl_seconds))
        
        # Calculate expiration timestamp
        expiration_time = time.time() + final_ttl
        
        logger.debug(f"Calculated TTL: {final_ttl}s (base: {base_ttl}s, jitter: {jitter:.1f}s, "
                    f"policy: {policy.value}, frequency: {access_frequency:.2f})")
        
        return final_ttl, expiration_time
    
    def is_expired(self, expiration_time: float, current_time: Optional[float] = None) -> bool:
        """Check if an entry has expired."""
        if current_time is None:
            current_time = time.time()
        return current_time >= expiration_time
    
    def should_refresh(self, 
                      expiration_time: float, 
                      policy: TTLPolicy,
                      current_time: Optional[float] = None) -> bool:
        """
        Check if an entry should be proactively refreshed.
        
        This helps avoid cache misses by refreshing entries before they expire.
        """
        if current_time is None:
            current_time = time.time()
        
        config = self.policies[policy]
        refresh_time = expiration_time - (config.base_ttl_seconds * (1.0 - config.refresh_threshold))
        
        return current_time >= refresh_time
    
    def get_policy_for_content(self, 
                              content_type: str,
                              analysis_mode: str = "standard",
                              indexer_reliability: float = 1.0) -> TTLPolicy:
        """
        Determine appropriate TTL policy for content.
        
        Args:
            content_type: 'movie' or 'tv'
            analysis_mode: 'standard', 'reliability', or 'hybrid'
            indexer_reliability: Reliability score of the indexer (0.0-1.0)
            
        Returns:
            Appropriate TTL policy
        """
        # Reliability mode uses shorter TTL for fresh data
        if analysis_mode == "reliability":
            return TTLPolicy.SHORT
        
        # Low reliability indexers get shorter TTL
        if indexer_reliability < 0.5:
            return TTLPolicy.SHORT
        elif indexer_reliability < 0.8:
            return TTLPolicy.MEDIUM
        
        # Standard mode with reliable indexers
        if content_type == "movie":
            # Movies are more stable, can use longer TTL
            return TTLPolicy.LONG
        else:  # TV shows
            # TV shows change more frequently (new episodes)
            return TTLPolicy.MEDIUM
    
    def calculate_synchronized_ttl(self, 
                                  l1_policy: TTLPolicy,
                                  l2_policy: TTLPolicy,
                                  content_type: str = "unknown") -> Tuple[int, int, float]:
        """
        Calculate synchronized TTL for L1 and L2 layers.
        
        Ensures that L1 doesn't outlive L2 and coordinates expiration.
        
        Args:
            l1_policy: Policy for L1 (memory) cache
            l2_policy: Policy for L2 (persistent) cache
            content_type: Type of content
            
        Returns:
            Tuple of (l1_ttl_seconds, l2_ttl_seconds, expiration_timestamp)
        """
        # Calculate L2 TTL first (it's the source of truth)
        l2_ttl, l2_expiration = self.calculate_ttl(l2_policy, content_type)
        
        # Calculate L1 TTL (should not exceed L2)
        l1_ttl, l1_expiration = self.calculate_ttl(l1_policy, content_type)
        
        # Ensure L1 doesn't outlive L2
        if l1_expiration > l2_expiration:
            l1_ttl = int(l2_ttl * 0.8)  # L1 expires 20% earlier than L2
            l1_expiration = time.time() + l1_ttl
        
        # Use the earlier expiration as the synchronized expiration
        sync_expiration = min(l1_expiration, l2_expiration)
        
        logger.debug(f"Synchronized TTL: L1={l1_ttl}s, L2={l2_ttl}s, sync_exp={sync_expiration}")
        
        return l1_ttl, l2_ttl, sync_expiration
    
    def update_policy(self, policy: TTLPolicy, config: TTLConfig):
        """Update a TTL policy configuration."""
        self.policies[policy] = config
        logger.info(f"Updated TTL policy {policy.value}: base={config.base_ttl_seconds}s, "
                   f"jitter={config.jitter_percent}%")
    
    def get_policy_config(self, policy: TTLPolicy) -> TTLConfig:
        """Get configuration for a TTL policy."""
        return self.policies[policy]
    
    def calculate_staggered_cleanup_time(self, 
                                       base_cleanup_interval: int,
                                       max_jitter_percent: float = 20.0) -> int:
        """
        Calculate staggered cleanup time to avoid synchronized cleanup operations.
        
        Args:
            base_cleanup_interval: Base interval between cleanups (seconds)
            max_jitter_percent: Maximum jitter as percentage of base interval
            
        Returns:
            Staggered cleanup interval in seconds
        """
        jitter_range = base_cleanup_interval * (max_jitter_percent / 100.0)
        jitter = random.uniform(-jitter_range, jitter_range)
        
        staggered_interval = int(base_cleanup_interval + jitter)
        staggered_interval = max(60, staggered_interval)  # Minimum 1 minute
        
        logger.debug(f"Staggered cleanup interval: {staggered_interval}s "
                    f"(base: {base_cleanup_interval}s, jitter: {jitter:.1f}s)")
        
        return staggered_interval
    
    def get_ttl_stats(self) -> Dict[str, Any]:
        """Get TTL coordinator statistics."""
        stats = {
            'policies': {},
            'current_time': time.time()
        }
        
        for policy, config in self.policies.items():
            stats['policies'][policy.value] = {
                'base_ttl_seconds': config.base_ttl_seconds,
                'jitter_percent': config.jitter_percent,
                'min_ttl_seconds': config.min_ttl_seconds,
                'max_ttl_seconds': config.max_ttl_seconds,
                'refresh_threshold': config.refresh_threshold
            }
        
        return stats


# Global TTL coordinator instance
_ttl_coordinator = None


def get_ttl_coordinator() -> TTLCoordinator:
    """Get the global TTL coordinator instance."""
    global _ttl_coordinator
    if _ttl_coordinator is None:
        _ttl_coordinator = TTLCoordinator()
    return _ttl_coordinator


def calculate_content_ttl(content_type: str, 
                         analysis_mode: str = "standard",
                         indexer_reliability: float = 1.0) -> Tuple[int, float]:
    """
    Convenience function to calculate TTL for content.
    
    Args:
        content_type: 'movie' or 'tv'
        analysis_mode: 'standard', 'reliability', or 'hybrid'
        indexer_reliability: Reliability score (0.0-1.0)
        
    Returns:
        Tuple of (ttl_seconds, expiration_timestamp)
    """
    coordinator = get_ttl_coordinator()
    policy = coordinator.get_policy_for_content(content_type, analysis_mode, indexer_reliability)
    return coordinator.calculate_ttl(policy, content_type)
