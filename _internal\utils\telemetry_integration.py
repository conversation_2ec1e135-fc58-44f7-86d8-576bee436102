#!/usr/bin/env python3
"""
Enhanced Telemetry Integration with Accurate Download Tracking

This module provides enhanced telemetry integration that addresses Phase 1 issues:
1. Movie ID to release name mapping for accurate status reporting
2. Radarr API integration for download confirmation  
3. Enhanced tracking of actual vs requested downloads

Key Features:
- Tracks correlation between Radarr movie IDs and actual release names
- Confirms downloads via Radarr moviefile API
- Provides accurate success/failure reporting
- Maps scene/P2P group names to original requests
"""

import asyncio
import logging
import time
import json
import aiohttp
from typing import Optional, Dict, Any, List
from pathlib import Path
from dataclasses import dataclass, field
from .real_time_telemetry import RealTimeTelemetry

@dataclass
class MovieTrackingRecord:
    """Enhanced tracking record for movie downloads with release name correlation."""
    radarr_id: int
    movie_title: str
    requested_quality: str
    job_id: str
    
    # Release tracking for accurate reporting
    actual_release_name: Optional[str] = None
    scene_group: Optional[str] = None
    quality_detected: Optional[str] = None
    
    # Status and confirmation
    status: str = "pending"  # pending, grabbed, downloaded, imported, failed
    moviefile_id: Optional[int] = None
    moviefile_path: Optional[str] = None
    
    # Timing for duration tracking
    requested_at: float = field(default_factory=time.time)
    confirmed_at: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for persistence."""
        return {
            'radarr_id': self.radarr_id,
            'movie_title': self.movie_title,
            'requested_quality': self.requested_quality,
            'job_id': self.job_id,
            'actual_release_name': self.actual_release_name,
            'scene_group': self.scene_group,
            'quality_detected': self.quality_detected,
            'status': self.status,
            'moviefile_id': self.moviefile_id,
            'moviefile_path': self.moviefile_path,
            'requested_at': self.requested_at,
            'confirmed_at': self.confirmed_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MovieTrackingRecord':
        """Create from dictionary."""
        return cls(**{k: v for k, v in data.items() if k in cls.__annotations__})

class TelemetryIntegrator:
    """
    Enhanced telemetry integrator with accurate movie ID to release name mapping.
    
    Provides convenience methods for common telemetry operations with enhanced
    tracking capabilities for Phase 1 improvements.
    """
    
    def __init__(self, settings_dict: dict, logger: Optional[logging.Logger] = None, verbose_mode: bool = False):
        """
        Initialize the enhanced telemetry integrator.
        
        Args:
            settings_dict: PlexAutomator settings dictionary
            logger: Optional logger instance
            verbose_mode: If True, shows detailed line-by-line output; if False, shows compact dashboard
        """
        self.settings_dict = settings_dict
        self.logger = logger or logging.getLogger("telemetry_integration")
        self.telemetry: Optional[RealTimeTelemetry] = None
        self._session_active = False
        self.verbose_mode = verbose_mode
        
        # Enhanced tracking for Phase 1
        self.movie_records: Dict[int, MovieTrackingRecord] = {}  # radarr_id -> record
        self.job_to_radarr_mapping: Dict[str, int] = {}  # job_id -> radarr_id
        
        # Radarr API configuration for download confirmation
        self.radarr_config = settings_dict.get('Radarr', {})
        self.radarr_url = self.radarr_config.get('url', '').rstrip('/')
        self.radarr_api_key = self.radarr_config.get('api_key', '')
        
        # State persistence for tracking
        self.enhanced_state_file = Path("_internal/state/telemetry_tracking.json")
        self.enhanced_state_file.parent.mkdir(parents=True, exist_ok=True)
    
    async def __aenter__(self):
        """Async context manager entry with enhanced tracking initialization."""
        # Extract telemetry config from settings
        config = self._build_telemetry_config()
        config['verbose_mode'] = self.verbose_mode  # Pass verbose mode to telemetry
        self.telemetry = RealTimeTelemetry(config, self.logger)
        await self.telemetry.__aenter__()
        self._session_active = True
        
        # Load enhanced tracking state
        self._load_enhanced_state()
        
        self.logger.info("🔬 Enhanced telemetry integration initialized")
        self.logger.info(f"   📊 Loaded {len(self.movie_records)} existing movie records")
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with enhanced state saving."""
        if self.telemetry:
            await self.telemetry.__aexit__(exc_type, exc_val, exc_tb)
        
        # Save enhanced tracking state
        self._save_enhanced_state()
        
        self._session_active = False
    
    def _build_telemetry_config(self) -> dict:
        """Build telemetry configuration from PlexAutomator settings."""
        telemetry_cfg = self.settings_dict.get("Telemetry", {})
        return {
            "Sonarr": {
                "enabled": self.settings_dict.get("Sonarr", {}).get("enabled", False),
                "url": self.settings_dict.get("Sonarr", {}).get("url", ""),  # Use 'url' not 'connection_url'
                "api_key": self.settings_dict.get("Sonarr", {}).get("api_key", "")
            },
            "Radarr": {
                "enabled": self.settings_dict.get("Radarr", {}).get("enabled", False),
                "url": self.settings_dict.get("Radarr", {}).get("url", ""),  # Use 'url' not 'connection_url'
                "api_key": self.settings_dict.get("Radarr", {}).get("api_key", "")
            },
            "SABnzbd": {
                "enabled": self.settings_dict.get("SABnzbd", {}).get("enabled", False),
                "base_url": self.settings_dict.get("SABnzbd", {}).get("base_url", ""),
                "api_key": self.settings_dict.get("SABnzbd", {}).get("api_key", "")
            },
            "telemetry_state_path": "_internal/state/telemetry_state.json",
            "telemetry_state_max_age_sec": int(telemetry_cfg.get("state_max_age_sec", 600)),
            # New behavior toggles
            "session_only": bool(telemetry_cfg.get("session_only", True)),
            "clear_on_start": bool(telemetry_cfg.get("clear_on_start", True)),
            "update_interval_sec": int(telemetry_cfg.get("update_interval_sec", 5)),
        }
    
    def track_movie_download(self, title: str, radarr_id: int, quality: Optional[str] = None, 
                           is_fallback_workflow: bool = False, nzb_filename: Optional[str] = None) -> str:
        """
        Track a new movie download with enhanced correlation tracking.
        Replaces static 'Movie added to Radarr' messages.
        
        Args:
            title: Movie title
            radarr_id: Radarr movie ID
            quality: Quality profile name
            is_fallback_workflow: True if this is a remove/re-add fallback scenario
            nzb_filename: Actual NZB filename for display instead of movie title
            
        Returns:
            job_id: Unique job identifier for tracking
        """
        if not self.telemetry or not self._session_active:
            self.logger.error("Telemetry not initialized - use async context manager")
            return ""
        
        job_id = self.telemetry.track_new_download(
            title=title,
            radarr_id=radarr_id,
            quality=quality,
            nzb_filename=nzb_filename,
            is_fallback_workflow=is_fallback_workflow
        )
        
        # Create enhanced tracking record for Phase 1 correlation
        if radarr_id and job_id:
            record = MovieTrackingRecord(
                radarr_id=radarr_id,
                movie_title=title,
                requested_quality=quality or "Unknown",
                job_id=job_id
            )
            
            self.movie_records[radarr_id] = record
            self.job_to_radarr_mapping[job_id] = radarr_id
            
            self.logger.info(f"📋 Enhanced tracking: {title}")
            self.logger.info(f"   🆔 Radarr ID: {radarr_id}")
            self.logger.info(f"   📊 Job ID: {job_id}")
            
            self._save_enhanced_state()
        
        return job_id
    
    def track_episode_download(self, title: str, sonarr_id: int, episode_id: Optional[int] = None, 
                             quality: Optional[str] = None, is_fallback_workflow: bool = False) -> str:
        """
        Track a new episode download. Replaces static 'Episode added to Sonarr' messages.
        
        Args:
            title: Episode title  
            sonarr_id: Sonarr series ID
            episode_id: Sonarr episode ID (if available)
            quality: Quality profile name
            is_fallback_workflow: True if this is a remove/re-add fallback scenario
            
        Returns:
            job_id: Unique job identifier for tracking
        """
        if not self.telemetry or not self._session_active:
            self.logger.error("Telemetry not initialized - use async context manager")
            return ""
        
        return self.telemetry.track_new_download(
            title=title,
            sonarr_id=sonarr_id,
            episode_id=episode_id,
            quality=quality,
            is_fallback_workflow=is_fallback_workflow
        )
    
    def update_nzb_filename(self, job_id: str, nzb_filename: str) -> bool:
        """
        Update the NZB filename for an existing job after preflight analysis.
        
        Args:
            job_id: The job ID to update
            nzb_filename: The actual NZB filename selected during preflight
            
        Returns:
            True if update successful, False if job not found or telemetry not active
        """
        if not self.telemetry or not self._session_active:
            self.logger.error("Telemetry not initialized - use async context manager")
            return False
        
        return self.telemetry.update_nzb_filename(job_id, nzb_filename)

    async def monitor_downloads(self, interval: int = 5, timeout: Optional[int] = None) -> bool:
        """
        Monitor all tracked downloads until completion.
        
        Args:
            interval: Polling interval in seconds
            timeout: Maximum time to wait (None for no timeout)
            
        Returns:
            True if all downloads completed, False if timeout or error
        """
        if not self.telemetry or not self._session_active:
            self.logger.error("Telemetry not initialized - use async context manager")
            return False
        
        try:
            if timeout:
                # Monitor with timeout
                return await self.telemetry.wait_for_completion(timeout)
            else:
                # Monitor indefinitely
                await self.telemetry.start_monitoring(interval)
                return True
        except Exception as e:
            self.logger.error(f"Error monitoring downloads: {e}")
            return False
    
    def get_active_download_count(self) -> int:
        """Get the number of currently active downloads."""
        if not self.telemetry:
            return 0
        return len(self.telemetry.active_jobs)
    
    def get_download_summary(self) -> dict:
        """Get a summary of current download status."""
        if not self.telemetry:
            return {"active_downloads": 0, "completed_downloads": 0, "active_jobs": []}
        return self.telemetry.get_summary()
    
    def clear_stale_state(self) -> int:
        """
        Clear stale test data and old entries from telemetry state.
        
        Returns:
            Number of entries removed
        """
        if not self.telemetry:
            self.logger.warning("Telemetry not initialized - cannot clear stale state")
            return 0
        return self.telemetry.clear_stale_state()
    
    # ========== PHASE 1 ENHANCED METHODS ==========
    
    async def confirm_downloads_via_radarr_api(self) -> Dict[int, Dict[str, Any]]:
        """
        Phase 1: Confirm actual downloads by checking Radarr's moviefile API.
        
        This addresses the core issue where telemetry shows "failed" for actually 
        successful downloads by directly querying what Radarr has imported.
        
        Returns:
            Dictionary mapping radarr_id to confirmed download details
        """
        if not self.radarr_api_key or not self.radarr_url:
            self.logger.error("Radarr API not configured for download confirmation")
            return {}
        
        confirmed_downloads = {}
        
        for radarr_id, record in self.movie_records.items():
            if record.status in ["imported", "failed"]:
                continue  # Skip already processed
            
            try:
                movie_files = await self._get_radarr_movie_files(radarr_id)
                
                if movie_files:
                    # Find the most recent movie file
                    latest_file = max(movie_files, key=lambda f: f.get('dateAdded', ''))
                    
                    # Update record with confirmed download
                    record.moviefile_id = latest_file.get('id')
                    record.moviefile_path = latest_file.get('relativePath', '')
                    record.status = "imported"
                    record.confirmed_at = time.time()
                    
                    # Extract release information from filename
                    if record.moviefile_path:
                        filename = Path(record.moviefile_path).stem
                        record.actual_release_name = filename
                        record.scene_group = self._extract_scene_group(filename)
                        record.quality_detected = self._detect_quality_from_name(filename)
                    
                    confirmed_downloads[radarr_id] = {
                        'movie_title': record.movie_title,
                        'requested_quality': record.requested_quality,
                        'actual_release_name': record.actual_release_name,
                        'scene_group': record.scene_group,
                        'detected_quality': record.quality_detected,
                        'file_path': record.moviefile_path,
                        'file_size_mb': latest_file.get('size', 0) // (1024*1024) if latest_file.get('size') else 0,
                        'success': True
                    }
                    
                    self.logger.info(f"✅ Confirmed download: {record.movie_title}")
                    self.logger.info(f"   📁 File: {record.moviefile_path}")
                    self.logger.info(f"   👥 Group: {record.scene_group}")
                    
                else:
                    # No movie files found - likely still downloading or failed
                    self.logger.debug(f"⏳ No movie files yet for: {record.movie_title}")
                
            except Exception as e:
                self.logger.error(f"Error confirming download for {record.movie_title}: {e}")
        
        self._save_enhanced_state()
        return confirmed_downloads
    
    async def _get_radarr_movie_files(self, radarr_id: int) -> List[Dict[str, Any]]:
        """Get movie files for a specific Radarr movie ID."""
        if not self.telemetry or not self.telemetry._session:
            return []
        
        try:
            url = f"{self.radarr_url}/api/v3/moviefile"
            headers = {'X-Api-Key': self.radarr_api_key}
            params = {'movieId': radarr_id}
            
            async with self.telemetry._session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    self.logger.warning(f"Failed to get movie files for {radarr_id}: HTTP {response.status}")
                    return []
                    
        except Exception as e:
            self.logger.error(f"Error getting movie files for {radarr_id}: {e}")
            return []
    
    def _extract_scene_group(self, release_name: str) -> Optional[str]:
        """Extract scene group from release name."""
        if not release_name:
            return None
        
        # Look for group after dash
        if '-' in release_name:
            potential_group = release_name.split('-')[-1]
            # Validate it looks like a group (no file extensions, reasonable length)
            if not potential_group.endswith(('.mkv', '.mp4', '.avi')) and len(potential_group) <= 20:
                return potential_group
        
        # Common scene groups to detect
        known_groups = [
            'FRAMETSTOR', 'GAZPROM', 'VEiL', 'PiR8', 'd3g', 'GMB',
            'SPARKS', 'AMIABLE', 'DEFLATE', 'RARBG', 'YTS', 'ETRG'
        ]
        
        release_upper = release_name.upper()
        for group in known_groups:
            if group.upper() in release_upper:
                return group
        
        return None
    
    def _detect_quality_from_name(self, release_name: str) -> Optional[str]:
        """Detect quality from release name."""
        if not release_name:
            return None
        
        release_upper = release_name.upper()
        
        if '2160P' in release_upper or '4K' in release_upper:
            return '4K'
        elif '1080P' in release_upper:
            return '1080p'
        elif '720P' in release_upper:
            return '720p'
        elif 'BLURAY' in release_upper or 'COMPLETE' in release_upper:
            return 'BluRay'
        
        return None
    
    def get_enhanced_status_report(self) -> Dict[str, Any]:
        """
        Phase 1: Get comprehensive status report with actual vs requested correlation.
        
        This provides the accurate reporting that was missing from the original telemetry.
        """
        total_tracked = len(self.movie_records)
        
        # Count statuses
        status_counts = {"pending": 0, "grabbed": 0, "imported": 0, "failed": 0}
        
        # Quality and group breakdowns
        quality_breakdown = {}
        scene_group_breakdown = {}
        
        # Detailed records
        detailed_records = []
        
        for record in self.movie_records.values():
            # Status counting
            status_counts[record.status] = status_counts.get(record.status, 0) + 1
            
            # Quality breakdown
            quality = record.quality_detected or record.requested_quality
            quality_breakdown[quality] = quality_breakdown.get(quality, 0) + 1
            
            # Scene group breakdown
            if record.scene_group:
                scene_group_breakdown[record.scene_group] = scene_group_breakdown.get(record.scene_group, 0) + 1
            
            # Detailed record
            duration_minutes = None
            if record.confirmed_at and record.requested_at:
                duration_minutes = round((record.confirmed_at - record.requested_at) / 60, 1)
            
            detailed_records.append({
                'movie_title': record.movie_title,
                'requested_quality': record.requested_quality,
                'actual_release_name': record.actual_release_name,
                'scene_group': record.scene_group,
                'detected_quality': record.quality_detected,
                'status': record.status,
                'success': record.status == 'imported',
                'duration_minutes': duration_minutes,
                'radarr_id': record.radarr_id
            })
        
        # Calculate accurate success rate
        success_rate = status_counts['imported'] / total_tracked if total_tracked > 0 else 0.0
        
        return {
            'total_tracked': total_tracked,
            'status_counts': status_counts,
            'success_rate': success_rate,
            'quality_breakdown': quality_breakdown,
            'scene_group_breakdown': scene_group_breakdown,
            'detailed_records': detailed_records,
            'mapping_accuracy': len([r for r in self.movie_records.values() if r.actual_release_name]) / total_tracked if total_tracked > 0 else 0.0
        }
    
    def _save_enhanced_state(self) -> None:
        """Save enhanced tracking state to disk."""
        try:
            state_data = {
                'movie_records': {str(k): v.to_dict() for k, v in self.movie_records.items()},
                'job_to_radarr_mapping': self.job_to_radarr_mapping,
                'last_updated': time.time()
            }
            
            with open(self.enhanced_state_file, 'w') as f:
                json.dump(state_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving enhanced telemetry state: {e}")
    
    def _load_enhanced_state(self) -> None:
        """Load enhanced tracking state from disk."""
        try:
            if not self.enhanced_state_file.exists():
                return
            
            with open(self.enhanced_state_file, 'r') as f:
                state_data = json.load(f)
            
            # Load movie records
            for radarr_id_str, record_data in state_data.get('movie_records', {}).items():
                radarr_id = int(radarr_id_str)
                self.movie_records[radarr_id] = MovieTrackingRecord.from_dict(record_data)
            
            # Load job mapping
            self.job_to_radarr_mapping = state_data.get('job_to_radarr_mapping', {})
            
        except Exception as e:
            self.logger.error(f"Error loading enhanced telemetry state: {e}")

    # ========== END PHASE 1 METHODS ==========

    async def check_monitoring_status(self, radarr_id: Optional[int] = None, sonarr_id: Optional[int] = None) -> bool:
        """
        Check if a movie/series is properly monitored in *arr.
        
        Args:
            radarr_id: Radarr movie ID to check
            sonarr_id: Sonarr series ID to check
            
        Returns:
            True if monitored, False if not monitored or error
        """
        if not self.telemetry or not self._session_active:
            self.logger.error("Telemetry not initialized - use async context manager")
            return False
            
        try:
            if radarr_id and self.telemetry.radarr_config.get('enabled', False):
                # Check Radarr movie monitoring status
                url = f"{self.telemetry.radarr_config['connection_url']}/api/v3/movie/{radarr_id}"
                headers = {'X-Api-Key': self.telemetry.radarr_config['api_key']}
                
                async with self.telemetry._session.get(url, headers=headers) as response:
                    if response.status == 200:
                        movie_data = await response.json()
                        return movie_data.get('monitored', False)
                    
            elif sonarr_id and self.telemetry.sonarr_config.get('enabled', False):
                # Check Sonarr series monitoring status  
                url = f"{self.telemetry.sonarr_config['connection_url']}/api/v3/series/{sonarr_id}"
                headers = {'X-Api-Key': self.telemetry.sonarr_config['api_key']}
                
                async with self.telemetry._session.get(url, headers=headers) as response:
                    if response.status == 200:
                        series_data = await response.json()
                        return series_data.get('monitored', False)
                        
        except Exception as e:
            self.logger.error(f"Error checking monitoring status: {e}")
            
        return False
    
    def add_webhook_support_note(self):
        """
        Log information about webhook support for real-time grab notifications.
        This can be used to inform users about setting up webhooks for better tracking.
        """
        webhook_info = """
🔔 WEBHOOK INTEGRATION AVAILABLE:
For instant grab notifications, configure Radarr/Sonarr webhooks:
- URL: http://your-system:port/webhook/grab
- Events: On Grab, On Import, On Health Issue  
- This eliminates polling delays and provides real-time status updates.
        """
        self.logger.info(webhook_info.strip())
        
    async def verify_grab_by_id(self, job_id: str, max_wait_sec: int = 120) -> bool:
        """
        Wait for a specific download job to be verified as grabbed/downloading.
        
        Args:
            job_id: Job ID returned by track_*_download methods
            max_wait_sec: Maximum time to wait for verification
            
        Returns:
            True if grab was verified, False if timeout or failed
        """
        if not self.telemetry or not self._session_active:
            self.logger.error("Telemetry not initialized - use async context manager")
            return False
            
        start_time = time.time()
        
        while time.time() - start_time < max_wait_sec:
            if job_id in self.telemetry.active_jobs:
                job = self.telemetry.active_jobs[job_id]
                if job.status in ["downloading", "completed"]:
                    return True
                elif job.status == "failed":
                    return False
            elif job_id in self.telemetry.completed_jobs:
                # Job completed successfully
                return True
                
            # Wait a bit before checking again
            await asyncio.sleep(2)
            
        return False


# Convenience functions for direct use (non-async environments)
def create_telemetry_integrator(settings_dict: dict, logger: Optional[logging.Logger] = None) -> TelemetryIntegrator:
    """
    Create a telemetry integrator instance.
    
    Args:
        settings_dict: PlexAutomator settings dictionary
        logger: Optional logger instance
        
    Returns:
        TelemetryIntegrator instance
    """
    return TelemetryIntegrator(settings_dict, logger)


async def track_and_monitor_downloads(settings_dict: dict, downloads: list, 
                                    logger: Optional[logging.Logger] = None,
                                    interval: int = 5) -> bool:
    """
    Convenience function to track multiple downloads and monitor them to completion.
    
    Args:
        settings_dict: PlexAutomator settings dictionary
        downloads: List of download dictionaries with keys: title, type (movie/episode), 
                  radarr_id/sonarr_id, episode_id (optional), quality (optional)
        logger: Optional logger instance
        interval: Polling interval in seconds
        
    Returns:
        True if all downloads completed successfully
        
    Example:
        downloads = [
            {"title": "Movie (2023)", "type": "movie", "radarr_id": 123, "quality": "HD-1080p"},
            {"title": "Show S01E01", "type": "episode", "sonarr_id": 456, "episode_id": 789}
        ]
        success = await track_and_monitor_downloads(settings, downloads, logger)
    """
    async with TelemetryIntegrator(settings_dict, logger) as integrator:
        # Track all downloads
        for download in downloads:
            if download.get("type") == "movie":
                integrator.track_movie_download(
                    title=download["title"],
                    radarr_id=download["radarr_id"],
                    quality=download.get("quality")
                )
            elif download.get("type") == "episode":
                integrator.track_episode_download(
                    title=download["title"],
                    sonarr_id=download["sonarr_id"],
                    episode_id=download.get("episode_id"),
                    quality=download.get("quality")
                )
        
        # Monitor until completion
        return await integrator.monitor_downloads(interval)


# Example integration patterns for existing scripts
class LegacyBridge:
    """
    Provides bridge methods to gradually replace static logging in existing code.
    
    These methods can be dropped into existing scripts as minimal-change replacements
    for print statements and static logging.
    """
    
    @staticmethod
    def replace_movie_success_log(title: str, radarr_id: int, integrator: TelemetryIntegrator):
        """
        Replace: print("✅ Successfully added to Radarr!")
        With: LegacyBridge.replace_movie_success_log(title, radarr_id, integrator)
        """
        integrator.track_movie_download(title, radarr_id)
    
    @staticmethod  
    def replace_episode_success_log(title: str, sonarr_id: int, integrator: TelemetryIntegrator,
                                  episode_id: Optional[int] = None):
        """
        Replace: print("✅ Episode added to Sonarr!")
        With: LegacyBridge.replace_episode_success_log(title, sonarr_id, integrator, episode_id)
        """
        integrator.track_episode_download(title, sonarr_id, episode_id)
    
    @staticmethod
    async def replace_monitoring_loop(integrator: TelemetryIntegrator, 
                                    original_monitor_function=None):
        """
        Replace manual monitoring loops with telemetry monitoring.
        
        Replace: 
            while downloads_active:
                check_status()
                time.sleep(10)
                
        With:
            await LegacyBridge.replace_monitoring_loop(integrator)
        """
        if integrator.get_active_download_count() > 0:
            success = await integrator.monitor_downloads()
            if success:
                print("🎉 All downloads completed!")
            else:
                print("⚠️ Some downloads may not have completed successfully")
        
        # Optionally run original cleanup/organization function
        if original_monitor_function and callable(original_monitor_function):
            await original_monitor_function()


# Integration example for 01_intake_and_nzb_search.py
async def example_intake_integration():
    """
    Example showing how to integrate telemetry into the intake script.
    
    This replaces static success messages with real verification.
    """
    # Mock settings (would come from actual settings.ini)
    settings_dict = {
        "Sonarr": {"enabled": True, "connection_url": "http://localhost:8989", "api_key": "key"},
        "Radarr": {"enabled": True, "connection_url": "http://localhost:7878", "api_key": "key"},
        "SABnzbd": {"enabled": True, "base_url": "http://localhost:8080", "api_key": "key"}
    }
    
    logger = logging.getLogger("intake")
    
    async with TelemetryIntegrator(settings_dict, logger) as integrator:
        # Instead of: print("✅ Movie added to Radarr!")
        integrator.track_movie_download("Example Movie (2023)", radarr_id=123, quality="HD-1080p")
        
        # Instead of: print("✅ Episode added to Sonarr!")  
        integrator.track_episode_download("Show S02E05", sonarr_id=456, episode_id=789)
        
        # Instead of: manually checking queues
        print("\n=== Starting Real-Time Download Monitoring ===")
        await integrator.monitor_downloads(interval=5)


# Integration example for 02_download_and_organize.py
async def example_organize_integration():
    """
    Example showing how to integrate telemetry into the organize script.
    
    This replaces manual queue polling with accurate telemetry.
    """
    settings_dict = {}  # From actual settings
    logger = logging.getLogger("organize")
    
    async with TelemetryIntegrator(settings_dict, logger) as integrator:
        # Check if there are any downloads to monitor from previous steps
        active_count = integrator.get_active_download_count()
        if active_count > 0:
            print(f"📥 Found {active_count} active downloads, monitoring until completion...")
            success = await integrator.monitor_downloads()
            
            if success:
                print("✅ All downloads completed - proceeding to organization")
                # Continue with existing organization logic
            else:
                print("⚠️ Some downloads incomplete - check logs for details")
        else:
            print("✅ No active downloads found - proceeding to organization")
            # Continue with existing organization logic


# ========== PHASE 1 ENHANCED INTEGRATION FUNCTIONS ==========

async def verify_downloads_with_enhanced_tracking(settings_dict: dict, logger: logging.Logger,
                                                 movie_requests: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Phase 1: Enhanced download verification that addresses telemetry accuracy issues.
    
    This function provides the accurate tracking that was missing, correlating
    Radarr movie IDs with actual downloaded release names.
    
    Args:
        settings_dict: PlexAutomator settings
        logger: Logger instance
        movie_requests: List of movie request data with radarr_id, title, etc.
        
    Returns:
        Comprehensive report with actual vs requested downloads
    """
    async with TelemetryIntegrator(settings_dict, logger) as integrator:
        # Track all movie requests with enhanced correlation
        for request in movie_requests:
            integrator.track_movie_download(
                title=request.get('title', 'Unknown'),
                radarr_id=request.get('radarr_id'),
                quality=request.get('quality', 'Unknown')
            )
        
        # Wait a moment for any immediate downloads to start
        await asyncio.sleep(10)
        
        # Confirm downloads via Radarr API - this is the key Phase 1 improvement
        confirmed_downloads = await integrator.confirm_downloads_via_radarr_api()
        
        # Get comprehensive status report with accurate correlation
        status_report = integrator.get_enhanced_status_report()
        
        logger.info(f"📊 Enhanced tracking results:")
        logger.info(f"   🎯 Total tracked: {status_report['total_tracked']}")
        logger.info(f"   ✅ Success rate: {status_report['success_rate']:.1%}")
        logger.info(f"   🔗 Mapping accuracy: {status_report['mapping_accuracy']:.1%}")
        
        return {
            'confirmed_downloads': confirmed_downloads,
            'status_report': status_report,
            'total_success_rate': status_report['success_rate'],
            'mapping_accuracy': status_report['mapping_accuracy']
        }

async def quick_download_status_check(settings_dict: dict, logger: logging.Logger) -> Dict[str, Any]:
    """
    Phase 1: Quick status check for existing downloads without new tracking.
    
    Returns current status of any previously tracked downloads by checking
    Radarr's actual imported files.
    """
    async with TelemetryIntegrator(settings_dict, logger) as integrator:
        # Check existing downloads via Radarr API
        confirmed_downloads = await integrator.confirm_downloads_via_radarr_api()
        
        # Get status report
        status_report = integrator.get_enhanced_status_report()
        
        if confirmed_downloads:
            logger.info(f"📋 Found {len(confirmed_downloads)} confirmed downloads")
            for radarr_id, details in confirmed_downloads.items():
                logger.info(f"   ✅ {details['movie_title']} → {details['scene_group']}")
        
        return {
            'confirmed_downloads': confirmed_downloads,
            'status_report': status_report
        }

async def display_enhanced_download_report(settings_dict: dict, logger: logging.Logger) -> None:
    """
    Phase 1: Display comprehensive download report addressing telemetry accuracy.
    
    This replaces the confusing "failed" status messages with accurate reporting
    of what actually downloaded vs what was requested.
    """
    report = await quick_download_status_check(settings_dict, logger)
    status_report = report['status_report']
    confirmed_downloads = report['confirmed_downloads']
    
    print("\n" + "="*60)
    print("📊 ENHANCED DOWNLOAD REPORT (Phase 1 Accuracy Fix)")
    print("="*60)
    
    print(f"\n🎯 OVERALL STATUS:")
    print(f"   📋 Total tracked: {status_report['total_tracked']}")
    print(f"   ✅ Success rate: {status_report['success_rate']:.1%}")
    print(f"   🔗 ID mapping accuracy: {status_report['mapping_accuracy']:.1%}")
    
    if status_report['scene_group_breakdown']:
        print(f"\n👥 SCENE GROUPS:")
        for group, count in status_report['scene_group_breakdown'].items():
            print(f"   {group}: {count} downloads")
    
    if status_report['quality_breakdown']:
        print(f"\n🎥 QUALITY BREAKDOWN:")
        for quality, count in status_report['quality_breakdown'].items():
            print(f"   {quality}: {count} downloads")
    
    print(f"\n📝 DETAILED RECORDS:")
    for record in status_report['detailed_records']:
        status_icon = "✅" if record['success'] else "❌"
        duration = f" ({record['duration_minutes']}m)" if record['duration_minutes'] else ""
        release_info = f" → {record['scene_group']}" if record['scene_group'] else ""
        
        print(f"   {status_icon} {record['movie_title']}{release_info}{duration}")
        if record['actual_release_name'] and record['actual_release_name'] != record['movie_title']:
            print(f"      📁 {record['actual_release_name']}")
    
    print("\n" + "="*60)


if __name__ == "__main__":
    # Quick test of enhanced functionality
    logging.basicConfig(level=logging.INFO)
    
    # Test the enhanced reporting
    async def test_enhanced_features():
        # Mock settings for testing
        settings_dict = {
            "Radarr": {
                "enabled": True,
                "url": "http://localhost:7878",
                "api_key": "test_key"
            }
        }
        logger = logging.getLogger("enhanced_test")
        
        # Test quick status check
        await display_enhanced_download_report(settings_dict, logger)
    
    asyncio.run(test_enhanced_features())
